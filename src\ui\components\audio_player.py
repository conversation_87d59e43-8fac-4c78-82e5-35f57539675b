#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放器组件 - 提供音频播放、波形显示和控制功能
"""

import os
import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QSlider, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer, QUrl
from PySide6.QtGui import QFont
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

from .waveform_widget import WaveformWidget


class AudioPlayerWidget(QWidget):
    """音频播放器组件"""
    
    # 信号定义
    position_changed = Signal(int)  # 播放位置变化
    duration_changed = Signal(int)  # 总时长变化
    state_changed = Signal(int)     # 播放状态变化
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化音频播放器
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # 播放状态
        self.is_playing = False
        self.current_file = None
        self.duration = 0
        
        # 创建界面
        self._create_ui()
        self._connect_signals()
        
        self.logger.debug("音频播放器组件初始化完成")
    
    def _create_ui(self):
        """创建用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 波形显示区域
        self.waveform_widget = WaveformWidget()
        self.waveform_widget.setMinimumHeight(100)
        self.waveform_widget.position_clicked.connect(self._on_waveform_clicked)
        main_layout.addWidget(self.waveform_widget)
        
        # 控制面板
        control_frame = QFrame()
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(8)
        
        # 播放控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 播放/暂停按钮
        self.play_button = QPushButton("▶")
        self.play_button.setFixedSize(50, 50)
        self.play_button.setStyleSheet("""
            QPushButton {
                border-radius: 25px;
                font-size: 20px;
                font-weight: bold;
                background-color: #58A6FF;
                color: white;
            }
            QPushButton:hover {
                background-color: #79C0FF;
            }
            QPushButton:pressed {
                background-color: #388BFD;
            }
        """)
        self.play_button.clicked.connect(self._toggle_playback)
        button_layout.addWidget(self.play_button)
        
        # 停止按钮
        self.stop_button = QPushButton("⏹")
        self.stop_button.setFixedSize(40, 40)
        self.stop_button.setStyleSheet("""
            QPushButton {
                border-radius: 20px;
                font-size: 16px;
                background-color: #F85149;
                color: white;
            }
            QPushButton:hover {
                background-color: #FF6B6B;
            }
        """)
        self.stop_button.clicked.connect(self._stop_playback)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        # 音量控制
        volume_label = QLabel("🔊")
        volume_label.setStyleSheet("color: #8B949E; font-size: 16px;")
        button_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setFixedWidth(100)
        self.volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #30363D;
                height: 6px;
                background: #1C2128;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #58A6FF;
                border: 1px solid #58A6FF;
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::sub-page:horizontal {
                background: #58A6FF;
                border-radius: 3px;
            }
        """)
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        button_layout.addWidget(self.volume_slider)
        
        control_layout.addLayout(button_layout)
        
        # 进度条和时间显示
        progress_layout = QHBoxLayout()
        progress_layout.setSpacing(10)
        
        # 当前时间
        self.current_time_label = QLabel("0:00")
        self.current_time_label.setStyleSheet("color: #8B949E; font-size: 12px;")
        self.current_time_label.setFixedWidth(40)
        progress_layout.addWidget(self.current_time_label)
        
        # 进度滑块
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setRange(0, 0)
        self.progress_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #30363D;
                height: 4px;
                background: #1C2128;
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #58A6FF;
                border: 1px solid #58A6FF;
                width: 12px;
                height: 12px;
                border-radius: 6px;
                margin: -4px 0;
            }
            QSlider::sub-page:horizontal {
                background: #58A6FF;
                border-radius: 2px;
            }
        """)
        self.progress_slider.sliderPressed.connect(self._on_progress_pressed)
        self.progress_slider.sliderReleased.connect(self._on_progress_released)
        progress_layout.addWidget(self.progress_slider)
        
        # 总时长
        self.duration_label = QLabel("0:00")
        self.duration_label.setStyleSheet("color: #8B949E; font-size: 12px;")
        self.duration_label.setFixedWidth(40)
        progress_layout.addWidget(self.duration_label)
        
        control_layout.addLayout(progress_layout)
        
        main_layout.addWidget(control_frame)
        
        # 文件信息显示
        self.file_info_label = QLabel("未加载音频文件")
        self.file_info_label.setAlignment(Qt.AlignCenter)
        self.file_info_label.setStyleSheet("color: #8B949E; font-size: 11px; margin: 5px;")
        main_layout.addWidget(self.file_info_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 媒体播放器信号
        self.media_player.positionChanged.connect(self._on_position_changed)
        self.media_player.durationChanged.connect(self._on_duration_changed)
        self.media_player.playbackStateChanged.connect(self._on_state_changed)
        
        # 定时器用于更新波形位置
        self.update_timer = QTimer()
        self.update_timer.setInterval(50)  # 50ms更新一次
        self.update_timer.timeout.connect(self._update_waveform_position)
    
    def load_audio(self, file_path: str) -> bool:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 加载成功返回True
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"音频文件不存在: {file_path}")
                return False
            
            # 停止当前播放
            self.media_player.stop()
            
            # 设置媒体源
            self.media_player.setSource(QUrl.fromLocalFile(file_path))
            self.current_file = file_path
            
            # 加载波形
            self.waveform_widget.load_audio(file_path)
            
            # 更新文件信息
            filename = Path(file_path).name
            self.file_info_label.setText(f"已加载: {filename}")
            
            self.logger.info(f"音频文件加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载音频文件失败: {e}")
            self.file_info_label.setText("加载失败")
            return False
    
    def _toggle_playback(self):
        """切换播放/暂停状态"""
        if not self.current_file:
            return
        
        if self.is_playing:
            self.media_player.pause()
        else:
            self.media_player.play()
    
    def _stop_playback(self):
        """停止播放"""
        self.media_player.stop()
        self.progress_slider.setValue(0)
        self.waveform_widget.update_position(0)
    
    def _on_volume_changed(self, value: int):
        """音量变化处理"""
        self.audio_output.setVolume(value / 100.0)
    
    def _on_position_changed(self, position: int):
        """播放位置变化处理"""
        if not self.progress_slider.isSliderDown():
            self.progress_slider.setValue(position)
        
        # 更新时间显示
        self.current_time_label.setText(self._format_time(position))
        
        # 发送信号
        self.position_changed.emit(position)
    
    def _on_duration_changed(self, duration: int):
        """总时长变化处理"""
        self.duration = duration
        self.progress_slider.setRange(0, duration)
        self.duration_label.setText(self._format_time(duration))
        
        # 发送信号
        self.duration_changed.emit(duration)
    
    def _on_state_changed(self, state):
        """播放状态变化处理"""
        if state == QMediaPlayer.PlayingState:
            self.is_playing = True
            self.play_button.setText("⏸")
            self.update_timer.start()
        else:
            self.is_playing = False
            self.play_button.setText("▶")
            self.update_timer.stop()
        
        # 发送信号
        self.state_changed.emit(state)
    
    def _on_progress_pressed(self):
        """进度条按下处理"""
        pass
    
    def _on_progress_released(self):
        """进度条释放处理"""
        position = self.progress_slider.value()
        self.media_player.setPosition(position)
    
    def _on_waveform_clicked(self, relative_position: float):
        """波形点击处理"""
        if self.duration > 0:
            position = int(self.duration * relative_position)
            self.media_player.setPosition(position)
    
    def _update_waveform_position(self):
        """更新波形显示位置"""
        if self.duration > 0:
            position = self.media_player.position()
            relative_position = position / self.duration
            self.waveform_widget.update_position(relative_position)
    
    def _format_time(self, milliseconds: int) -> str:
        """
        格式化时间显示
        
        Args:
            milliseconds: 毫秒数
            
        Returns:
            str: 格式化的时间字符串 (mm:ss)
        """
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes}:{seconds:02d}"
