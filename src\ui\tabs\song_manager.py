#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
歌曲管理选项卡 - 负责成品歌曲的预览、播放和管理
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QScrollArea, 
    QWidget, QFrame, QFileDialog, QListWidget, QListWidgetItem,
    QSplitter, QGroupBox, QGridLayout
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap

from .base_tab import BaseTab
from core.config_manager import ConfigManager
from ui.components.audio_player import AudioPlayerWidget


class SongItemWidget(QFrame):
    """歌曲条目组件"""
    
    play_requested = Signal(dict)  # 播放请求信号
    delete_requested = Signal(dict)  # 删除请求信号
    
    def __init__(self, song_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        初始化歌曲条目
        
        Args:
            song_data: 歌曲数据字典
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.song_data = song_data
        self._setup_ui()
    
    def _setup_ui(self):
        """设置界面"""
        self.setFixedHeight(80)
        self.setStyleSheet("""
            SongItemWidget {
                border: 1px solid #30363D;
                border-radius: 8px;
                background-color: #1C2128;
                margin: 2px;
            }
            
            SongItemWidget:hover {
                background-color: #21262D;
                border-color: #58A6FF;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # 歌曲信息区域
        info_layout = QVBoxLayout()
        info_layout.setSpacing(4)
        
        # 歌曲名称
        self.title_label = QLabel(self.song_data.get('name', '未知歌曲'))
        self.title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        self.title_label.setStyleSheet("color: #E6EDF3;")
        info_layout.addWidget(self.title_label)
        
        # 歌曲详情
        details = []
        if self.song_data.get('model'):
            details.append(f"模型: {self.song_data['model']}")
        if self.song_data.get('created_time'):
            details.append(f"创建: {self.song_data['created_time']}")
        if self.song_data.get('duration'):
            details.append(f"时长: {self.song_data['duration']}")
        
        self.details_label = QLabel(" · ".join(details))
        self.details_label.setFont(QFont("Microsoft YaHei UI", 10))
        self.details_label.setStyleSheet("color: #8B949E;")
        info_layout.addWidget(self.details_label)
        
        layout.addLayout(info_layout, 1)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        # 播放按钮
        self.play_button = QPushButton("▶")
        self.play_button.setFixedSize(40, 40)
        self.play_button.setStyleSheet("""
            QPushButton {
                border-radius: 20px;
                font-size: 16px;
                background-color: #58A6FF;
            }
            QPushButton:hover {
                background-color: #79C0FF;
            }
        """)
        self.play_button.clicked.connect(self._on_play_clicked)
        button_layout.addWidget(self.play_button)
        
        # 删除按钮
        self.delete_button = QPushButton("🗑")
        self.delete_button.setFixedSize(40, 40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                border-radius: 20px;
                font-size: 14px;
                background-color: #F85149;
            }
            QPushButton:hover {
                background-color: #FF6B6B;
            }
        """)
        self.delete_button.clicked.connect(self._on_delete_clicked)
        button_layout.addWidget(self.delete_button)
        
        layout.addLayout(button_layout)
    
    def _on_play_clicked(self):
        """播放按钮点击处理"""
        self.play_requested.emit(self.song_data)
    
    def _on_delete_clicked(self):
        """删除按钮点击处理"""
        self.delete_requested.emit(self.song_data)


class SongManagerTab(BaseTab):
    """歌曲管理选项卡"""
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """初始化歌曲管理选项卡"""
        self.finished_songs = []  # 成品歌曲列表
        self.current_playing_song = None  # 当前播放的歌曲
        
        super().__init__(config_manager, parent)
        
        # 加载歌曲数据
        self._load_finished_songs()
    
    def _create_content(self):
        """创建选项卡内容"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(splitter)
        
        # 左侧：歌曲列表
        self._create_song_list_panel(splitter)
        
        # 右侧：播放器和详情
        self._create_player_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
    
    def _create_song_list_panel(self, parent):
        """创建歌曲列表面板"""
        list_frame = self.create_section_frame("成品歌曲")
        
        # 获取框架的布局
        list_layout = list_frame.layout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self._refresh_song_list)
        toolbar_layout.addWidget(self.refresh_button)
        
        self.import_button = QPushButton("📁 导入")
        self.import_button.clicked.connect(self._import_song)
        toolbar_layout.addWidget(self.import_button)
        
        toolbar_layout.addStretch()
        
        self.song_count_label = QLabel(f"共 {len(self.finished_songs)} 首歌曲")
        self.song_count_label.setStyleSheet("color: #8B949E;")
        toolbar_layout.addWidget(self.song_count_label)
        
        list_layout.addLayout(toolbar_layout)
        
        # 歌曲列表滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.song_list_widget = QWidget()
        self.song_list_layout = QVBoxLayout(self.song_list_widget)
        self.song_list_layout.setContentsMargins(0, 0, 0, 0)
        self.song_list_layout.setSpacing(5)
        
        scroll_area.setWidget(self.song_list_widget)
        list_layout.addWidget(scroll_area)
        
        parent.addWidget(list_frame)
    
    def _create_player_panel(self, parent):
        """创建播放器面板"""
        player_frame = self.create_section_frame("播放器")
        
        # 获取框架的布局
        player_layout = player_frame.layout()
        
        # 当前播放信息
        self.current_song_label = QLabel("未选择歌曲")
        self.current_song_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Bold))
        self.current_song_label.setAlignment(Qt.AlignCenter)
        self.current_song_label.setStyleSheet("color: #E6EDF3; margin: 20px;")
        player_layout.addWidget(self.current_song_label)
        
        # 音频播放器组件
        self.audio_player = AudioPlayerWidget()
        self.audio_player.setMinimumHeight(200)
        player_layout.addWidget(self.audio_player)
        
        # 分轨播放区域
        tracks_frame = self.create_section_frame("分轨播放")
        tracks_layout = tracks_frame.layout()
        
        # 分轨按钮网格
        tracks_grid = QGridLayout()
        
        # 人声轨道
        self.vocal_button = QPushButton("🎤 人声")
        self.vocal_button.setMinimumHeight(50)
        self.vocal_button.clicked.connect(lambda: self._play_track('vocal'))
        tracks_grid.addWidget(self.vocal_button, 0, 0)
        
        # 伴奏轨道
        self.instrumental_button = QPushButton("🎵 伴奏")
        self.instrumental_button.setMinimumHeight(50)
        self.instrumental_button.clicked.connect(lambda: self._play_track('instrumental'))
        tracks_grid.addWidget(self.instrumental_button, 0, 1)
        
        # 混音轨道
        self.mix_button = QPushButton("🎧 混音")
        self.mix_button.setMinimumHeight(50)
        self.mix_button.clicked.connect(lambda: self._play_track('mix'))
        tracks_grid.addWidget(self.mix_button, 1, 0)
        
        # 原始轨道
        self.original_button = QPushButton("📻 原始")
        self.original_button.setMinimumHeight(50)
        self.original_button.clicked.connect(lambda: self._play_track('original'))
        tracks_grid.addWidget(self.original_button, 1, 1)
        
        tracks_layout.addLayout(tracks_grid)
        player_layout.addWidget(tracks_frame)
        
        # 初始状态下禁用分轨按钮
        self._set_track_buttons_enabled(False)
        
        parent.addWidget(player_frame)
    
    def _load_finished_songs(self):
        """加载成品歌曲数据"""
        try:
            # 从outputs目录扫描歌曲
            outputs_dir = Path("outputs")
            if not outputs_dir.exists():
                return
            
            self.finished_songs = []
            
            for song_dir in outputs_dir.iterdir():
                if song_dir.is_dir():
                    song_info = self._parse_song_directory(song_dir)
                    if song_info:
                        self.finished_songs.append(song_info)
            
            # 按创建时间排序
            self.finished_songs.sort(key=lambda x: x.get('created_time', ''), reverse=True)
            
            self._update_song_list_display()
            
        except Exception as e:
            self.logger.error(f"加载歌曲列表失败: {e}")
    
    def _parse_song_directory(self, song_dir: Path) -> Optional[Dict[str, Any]]:
        """解析歌曲目录，提取歌曲信息"""
        try:
            song_info = {
                'name': song_dir.name,
                'path': str(song_dir),
                'created_time': song_dir.stat().st_mtime,
                'tracks': {}
            }
            
            # 查找各种音轨文件
            for file_path in song_dir.glob("*.wav"):
                filename = file_path.stem.lower()
                if 'vocal' in filename:
                    song_info['tracks']['vocal'] = str(file_path)
                elif 'instrumental' in filename or 'karaoke' in filename:
                    song_info['tracks']['instrumental'] = str(file_path)
                elif 'mix' in filename:
                    song_info['tracks']['mix'] = str(file_path)
                else:
                    song_info['tracks']['original'] = str(file_path)
            
            # 格式化创建时间
            import time
            song_info['created_time'] = time.strftime(
                '%Y-%m-%d %H:%M', 
                time.localtime(song_info['created_time'])
            )
            
            return song_info if song_info['tracks'] else None
            
        except Exception as e:
            self.logger.error(f"解析歌曲目录失败 {song_dir}: {e}")
            return None
    
    def _update_song_list_display(self):
        """更新歌曲列表显示"""
        # 清除现有项目
        for i in reversed(range(self.song_list_layout.count())):
            child = self.song_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 添加歌曲项目
        for song_data in self.finished_songs:
            song_item = SongItemWidget(song_data)
            song_item.play_requested.connect(self._on_song_play_requested)
            song_item.delete_requested.connect(self._on_song_delete_requested)
            self.song_list_layout.addWidget(song_item)
        
        # 添加弹性空间
        self.song_list_layout.addStretch()
        
        # 更新计数
        self.song_count_label.setText(f"共 {len(self.finished_songs)} 首歌曲")
    
    def _refresh_song_list(self):
        """刷新歌曲列表"""
        self.emit_status_message("正在刷新歌曲列表...")
        self._load_finished_songs()
        self.emit_status_message("歌曲列表已刷新")
    
    def _import_song(self):
        """导入歌曲"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.m4a)"
        )
        
        if file_path:
            # TODO: 实现歌曲导入逻辑
            self.show_info_message("导入歌曲", f"将导入: {file_path}")
    
    def _on_song_play_requested(self, song_data: Dict[str, Any]):
        """处理歌曲播放请求"""
        self.current_playing_song = song_data
        self.current_song_label.setText(song_data['name'])
        
        # 启用分轨按钮
        self._set_track_buttons_enabled(True)
        
        # 默认播放混音轨道
        if 'mix' in song_data['tracks']:
            self._play_track('mix')
        elif 'vocal' in song_data['tracks']:
            self._play_track('vocal')
        else:
            # 播放第一个可用轨道
            first_track = next(iter(song_data['tracks'].keys()))
            self._play_track(first_track)
    
    def _on_song_delete_requested(self, song_data: Dict[str, Any]):
        """处理歌曲删除请求"""
        from PySide6.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除歌曲 '{song_data['name']}' 吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # TODO: 实现删除逻辑
            self.show_info_message("删除歌曲", f"将删除: {song_data['name']}")
    
    def _play_track(self, track_type: str):
        """播放指定轨道"""
        if not self.current_playing_song:
            return
        
        tracks = self.current_playing_song.get('tracks', {})
        if track_type not in tracks:
            self.show_warning_message("播放失败", f"未找到 {track_type} 轨道")
            return
        
        file_path = tracks[track_type]
        if not os.path.exists(file_path):
            self.show_error_message("播放失败", f"文件不存在: {file_path}")
            return
        
        # 使用音频播放器播放
        self.audio_player.load_audio(file_path)
        self.emit_status_message(f"正在播放: {track_type} 轨道")
    
    def _set_track_buttons_enabled(self, enabled: bool):
        """设置分轨按钮启用状态"""
        self.vocal_button.setEnabled(enabled)
        self.instrumental_button.setEnabled(enabled)
        self.mix_button.setEnabled(enabled)
        self.original_button.setEnabled(enabled)
