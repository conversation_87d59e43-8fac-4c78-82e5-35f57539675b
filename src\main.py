#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
木偶AI翻唱应用 - 主入口文件
基于PySide6的模块化GUI应用程序
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt, QDir
    from PySide6.QtGui import QIcon
except ImportError as e:
    print(f"PySide6导入错误: {e}")
    print("请安装PySide6: pip install PySide6")
    sys.exit(1)

from ui.main_window import MainWindow
from core.config_manager import ConfigManager
from utils.logging_utils import setup_logging


def setup_application():
    """设置应用程序基本配置"""
    # 在Qt6中，高DPI支持默认启用，无需手动设置已弃用的属性
    # QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)  # 已弃用
    # QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)     # 已弃用

    # 创建应用程序实例
    app = QApplication(sys.argv)
    app.setApplicationName("AI翻唱")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("MuOu Studio")

    # 设置应用程序图标
    icon_path = project_root / "assets" / "images" / "title.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    return app


def main():
    """主函数"""
    try:
        # 设置日志
        logger = setup_logging()
        logger.info("=== 木偶AI翻唱应用启动 ===")
        logger.info(f"项目根目录: {project_root}")
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        logger.info("配置管理器初始化完成")
        
        # 设置应用程序
        app = setup_application()
        logger.info("应用程序基础配置完成")
        
        # 创建主窗口
        main_window = MainWindow(config_manager)
        main_window.show()
        logger.info("主窗口创建并显示完成")
        
        # 运行应用程序
        logger.info("应用程序开始运行")
        exit_code = app.exec()
        
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        logging.error(f"应用程序启动失败: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
