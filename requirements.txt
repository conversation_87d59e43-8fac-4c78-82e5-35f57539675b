# 木偶AI翻唱 v2.0 依赖包

# 核心GUI框架
PySide6>=6.5.0

# HTTP请求和API通信
requests>=2.28.0

# 音频处理
pydub>=0.25.1
numpy>=1.21.0

# 波形显示和图形
pyqtgraph>=0.13.0

# API服务框架
fastapi>=0.100.0
uvicorn[standard]>=0.22.0

# 数据验证和序列化
pydantic>=2.0.0

# 文件上传处理
python-multipart>=0.0.6

# 可选依赖 - 高级音频处理
# librosa>=0.10.0
# torch>=2.0.0
# torchaudio>=2.0.0

# 可选依赖 - 科学计算
# scipy>=1.9.0
# scikit-learn>=1.1.0

# 开发和测试依赖
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=22.0.0
# flake8>=5.0.0
