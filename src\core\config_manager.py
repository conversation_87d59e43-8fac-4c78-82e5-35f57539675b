#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 负责应用程序配置的加载、保存和管理
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class APIConfig:
    """API配置数据类"""
    local_enabled: bool = True
    local_host: str = "127.0.0.1"
    local_port: int = 8000
    remote_enabled: bool = False
    remote_url: str = ""
    auto_fallback: bool = True
    timeout: int = 30


@dataclass
class AudioConfig:
    """音频配置数据类"""
    sample_rate: int = 44100
    default_format: str = "wav"
    max_file_size_mb: int = 500
    temp_dir: str = "temp"


@dataclass
class UIConfig:
    """界面配置数据类"""
    theme: str = "dark"
    window_width: int = 1250
    window_height: int = 1000
    remember_window_state: bool = True
    language: str = "zh_CN"


@dataclass
class ProcessingConfig:
    """处理配置数据类"""
    default_model: str = ""
    vocal_pitch: int = 0
    instrumental_pitch: int = 0
    reverb_enabled: bool = False
    reverb_room_size: float = 0.3
    reverb_damping: float = 0.1
    reverb_wet_level: float = 0.2
    reverb_dry_level: float = 0.9
    # 高级参数
    vocoder: str = "pc_nsf_hifigan_testing"
    f0_extractor: str = "rmvpe (默认)"
    formant_shift: int = 0
    sampling_steps: int = 50
    sampler: str = "euler"
    device: str = "CPU"


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        self.logger = logging.getLogger(__name__)
        
        # 设置配置目录
        if config_dir is None:
            project_root = Path(__file__).parent.parent.parent
            self.config_dir = project_root / "config"
        else:
            self.config_dir = config_dir
            
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / "app_config.json"
        
        # 初始化配置对象
        self.api_config = APIConfig()
        self.audio_config = AudioConfig()
        self.ui_config = UIConfig()
        self.processing_config = ProcessingConfig()
        
        # 加载配置
        self.load_config()
        
        self.logger.info(f"配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def load_config(self) -> bool:
        """
        从文件加载配置
        
        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            if not self.config_file.exists():
                self.logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置
                return True
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载各个配置部分
            if 'api' in config_data:
                self.api_config = APIConfig(**config_data['api'])
            
            if 'audio' in config_data:
                self.audio_config = AudioConfig(**config_data['audio'])
            
            if 'ui' in config_data:
                self.ui_config = UIConfig(**config_data['ui'])
            
            if 'processing' in config_data:
                self.processing_config = ProcessingConfig(**config_data['processing'])
            
            self.logger.info("配置文件加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            config_data = {
                'api': asdict(self.api_config),
                'audio': asdict(self.audio_config),
                'ui': asdict(self.ui_config),
                'processing': asdict(self.processing_config)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        return self.api_config
    
    def get_audio_config(self) -> AudioConfig:
        """获取音频配置"""
        return self.audio_config
    
    def get_ui_config(self) -> UIConfig:
        """获取界面配置"""
        return self.ui_config
    
    def get_processing_config(self) -> ProcessingConfig:
        """获取处理配置"""
        return self.processing_config
    
    def update_api_config(self, **kwargs) -> bool:
        """更新API配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.api_config, key):
                    setattr(self.api_config, key, value)
            return self.save_config()
        except Exception as e:
            self.logger.error(f"更新API配置失败: {e}")
            return False
    
    def update_processing_config(self, **kwargs) -> bool:
        """更新处理配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.processing_config, key):
                    setattr(self.processing_config, key, value)
            return self.save_config()
        except Exception as e:
            self.logger.error(f"更新处理配置失败: {e}")
            return False
