#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI音频处理服务器 - 提供音频分离、音色转换和混音等API服务
"""

import os
import sys
import logging
import argparse
import asyncio
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
    from fastapi.responses import JSONResponse, FileResponse
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    from pydantic import BaseModel
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    print("FastAPI依赖不可用，请安装: pip install fastapi uvicorn")
    sys.exit(1)


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
app = FastAPI(
    title="木偶AI翻唱API服务",
    description="提供音频分离、音色转换和混音等功能的API服务",
    version="2.0.0"
)

# 任务状态存储
task_status: Dict[str, Dict[str, Any]] = {}

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str  # pending, running, completed, failed
    progress: int  # 0-100
    message: str
    result: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime


class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str
    path: str
    size: int
    modified_time: datetime
    config_file: Optional[str] = None


class ConfigInfo(BaseModel):
    """配置文件信息模型"""
    name: str
    path: str
    size: int
    modified_time: datetime


def scan_models_directory() -> Dict[str, List[Dict[str, Any]]]:
    """扫描models目录，获取模型和配置文件列表"""
    try:
        models_dir = Path("models")
        if not models_dir.exists():
            models_dir.mkdir()
            logger.warning("models目录不存在，已创建")
        
        models = []
        configs = []
        
        # 扫描模型文件
        for model_file in models_dir.glob("*.pt"):
            try:
                stat = model_file.stat()
                model_info = {
                    "name": model_file.name,
                    "path": str(model_file),
                    "size": stat.st_size,
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "config_file": None
                }
                
                # 查找对应的配置文件
                config_file = models_dir / f"{model_file.stem}.yaml"
                if config_file.exists():
                    model_info["config_file"] = config_file.name
                
                models.append(model_info)
                
            except Exception as e:
                logger.error(f"处理模型文件失败 {model_file}: {e}")
        
        # 扫描配置文件
        for config_file in models_dir.glob("*.yaml"):
            try:
                stat = config_file.stat()
                config_info = {
                    "name": config_file.name,
                    "path": str(config_file),
                    "size": stat.st_size,
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
                configs.append(config_info)
                
            except Exception as e:
                logger.error(f"处理配置文件失败 {config_file}: {e}")
        
        return {
            "models": models,
            "configs": configs
        }
        
    except Exception as e:
        logger.error(f"扫描models目录失败: {e}")
        return {"models": [], "configs": []}


def create_task(task_type: str, description: str) -> str:
    """创建新任务"""
    task_id = str(uuid.uuid4())
    now = datetime.now()
    
    task_status[task_id] = {
        "task_id": task_id,
        "type": task_type,
        "status": "pending",
        "progress": 0,
        "message": description,
        "result": None,
        "created_at": now,
        "updated_at": now
    }
    
    logger.info(f"创建任务: {task_id} - {description}")
    return task_id


def update_task_progress(task_id: str, progress: int, message: str, status: str = "running"):
    """更新任务进度"""
    if task_id in task_status:
        task_status[task_id].update({
            "status": status,
            "progress": progress,
            "message": message,
            "updated_at": datetime.now()
        })
        logger.info(f"任务进度更新: {task_id} - {progress}% - {message}")


def complete_task(task_id: str, success: bool, result: Optional[Dict[str, Any]] = None, message: str = ""):
    """完成任务"""
    if task_id in task_status:
        task_status[task_id].update({
            "status": "completed" if success else "failed",
            "progress": 100 if success else task_status[task_id]["progress"],
            "message": message or ("任务完成" if success else "任务失败"),
            "result": result,
            "updated_at": datetime.now()
        })
        logger.info(f"任务完成: {task_id} - {'成功' if success else '失败'}")


async def simulate_audio_processing(task_id: str, process_type: str, duration: int = 10):
    """模拟音频处理过程"""
    try:
        update_task_progress(task_id, 10, f"开始{process_type}处理...")
        await asyncio.sleep(1)
        
        update_task_progress(task_id, 30, f"正在进行{process_type}分析...")
        await asyncio.sleep(2)
        
        update_task_progress(task_id, 60, f"正在执行{process_type}算法...")
        await asyncio.sleep(3)
        
        update_task_progress(task_id, 90, f"正在生成{process_type}结果...")
        await asyncio.sleep(2)
        
        # 模拟生成结果文件
        result = {
            "output_files": [
                f"output_{process_type}_{task_id[:8]}.wav"
            ],
            "processing_time": duration,
            "quality_score": 0.95
        }
        
        complete_task(task_id, True, result, f"{process_type}处理完成")
        
    except Exception as e:
        logger.error(f"模拟处理失败: {e}")
        complete_task(task_id, False, None, f"{process_type}处理失败: {str(e)}")


# API路由定义

@app.get("/")
async def root():
    """根路径"""
    return {"message": "木偶AI翻唱API服务", "version": "2.0.0", "status": "running"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }


@app.get("/models/list")
async def get_models():
    """获取可用模型列表"""
    try:
        scan_result = scan_models_directory()
        return {
            "success": True,
            "models": scan_result["models"],
            "count": len(scan_result["models"])
        }
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/models/configs")
async def get_configs():
    """获取可用配置文件列表"""
    try:
        scan_result = scan_models_directory()
        return {
            "success": True,
            "configs": scan_result["configs"],
            "count": len(scan_result["configs"])
        }
    except Exception as e:
        logger.error(f"获取配置列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/models/scan")
async def scan_models():
    """重新扫描models目录"""
    try:
        scan_result = scan_models_directory()
        return {
            "success": True,
            "message": "模型目录扫描完成",
            "models_count": len(scan_result["models"]),
            "configs_count": len(scan_result["configs"]),
            "models": scan_result["models"],
            "configs": scan_result["configs"]
        }
    except Exception as e:
        logger.error(f"扫描模型目录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/separate")
async def separate_audio(
    background_tasks: BackgroundTasks,
    audio: UploadFile = File(...),
    mode: str = Form("vocals")
):
    """音频分离"""
    try:
        # 创建任务
        task_id = create_task("separation", f"音频分离 - {mode}模式")
        
        # 保存上传的文件
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        file_path = upload_dir / f"{task_id}_{audio.filename}"
        with open(file_path, "wb") as f:
            content = await audio.read()
            f.write(content)
        
        # 启动后台处理
        background_tasks.add_task(simulate_audio_processing, task_id, "音频分离")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "音频分离任务已启动"
        }
        
    except Exception as e:
        logger.error(f"音频分离失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/convert")
async def convert_voice(
    background_tasks: BackgroundTasks,
    vocal: UploadFile = File(...),
    model_file: str = Form(...),
    config_file: str = Form(...),
    params: str = Form("{}")
):
    """音色转换"""
    try:
        # 创建任务
        task_id = create_task("conversion", f"音色转换 - {model_file}")
        
        # 保存上传的文件
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        file_path = upload_dir / f"{task_id}_{vocal.filename}"
        with open(file_path, "wb") as f:
            content = await vocal.read()
            f.write(content)
        
        # 启动后台处理
        background_tasks.add_task(simulate_audio_processing, task_id, "音色转换")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "音色转换任务已启动"
        }
        
    except Exception as e:
        logger.error(f"音色转换失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/mix")
async def mix_audio(
    background_tasks: BackgroundTasks,
    vocal: UploadFile = File(...),
    instrumental: UploadFile = File(...),
    params: str = Form("{}")
):
    """音频混音"""
    try:
        # 创建任务
        task_id = create_task("mixing", "音频混音")
        
        # 保存上传的文件
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        vocal_path = upload_dir / f"{task_id}_vocal_{vocal.filename}"
        with open(vocal_path, "wb") as f:
            content = await vocal.read()
            f.write(content)
        
        instrumental_path = upload_dir / f"{task_id}_instrumental_{instrumental.filename}"
        with open(instrumental_path, "wb") as f:
            content = await instrumental.read()
            f.write(content)
        
        # 启动后台处理
        background_tasks.add_task(simulate_audio_processing, task_id, "音频混音")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "音频混音任务已启动"
        }
        
    except Exception as e:
        logger.error(f"音频混音失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/progress/{task_id}")
async def get_task_progress(task_id: str):
    """获取任务进度"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = task_status[task_id]
    return {
        "success": True,
        "task": {
            "task_id": task["task_id"],
            "status": task["status"],
            "progress": task["progress"],
            "message": task["message"],
            "result": task["result"],
            "created_at": task["created_at"].isoformat(),
            "updated_at": task["updated_at"].isoformat()
        }
    }


@app.get("/tasks")
async def get_all_tasks():
    """获取所有任务状态"""
    tasks = []
    for task in task_status.values():
        tasks.append({
            "task_id": task["task_id"],
            "type": task.get("type", "unknown"),
            "status": task["status"],
            "progress": task["progress"],
            "message": task["message"],
            "created_at": task["created_at"].isoformat(),
            "updated_at": task["updated_at"].isoformat()
        })
    
    return {
        "success": True,
        "tasks": tasks,
        "count": len(tasks)
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="木偶AI翻唱API服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    
    args = parser.parse_args()
    
    logger.info(f"启动API服务器: {args.host}:{args.port}")
    
    # 确保必要的目录存在
    Path("uploads").mkdir(exist_ok=True)
    Path("outputs").mkdir(exist_ok=True)
    Path("models").mkdir(exist_ok=True)
    
    # 启动服务器
    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )


if __name__ == "__main__":
    main()
