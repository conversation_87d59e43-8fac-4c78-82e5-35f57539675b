#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GUI测试脚本
"""

import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget
    from PySide6.QtCore import Qt
    
    print("PySide6导入成功")
    
    # 创建简单的测试应用
    app = QApplication(sys.argv)
    
    window = QWidget()
    window.setWindowTitle("GUI测试")
    window.resize(400, 300)
    
    layout = QVBoxLayout()
    label = QLabel("GUI重构完成！\n\n主要改进：\n- 修复了弃用警告\n- 完全按照HTML布局重新设计\n- 深色主题配色\n- 左右两栏布局")
    label.setAlignment(Qt.AlignCenter)
    label.setStyleSheet("""
        QLabel {
            background-color: #111827;
            color: #E5E7EB;
            font-size: 16px;
            padding: 20px;
            border-radius: 8px;
        }
    """)
    
    layout.addWidget(label)
    window.setLayout(layout)
    window.setStyleSheet("background-color: #1F2937;")
    
    window.show()
    
    print("测试窗口已显示")
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PySide6")
except Exception as e:
    print(f"其他错误: {e}")
