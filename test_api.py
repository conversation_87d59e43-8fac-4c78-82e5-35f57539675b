#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器测试脚本
"""

import requests
import json
import time


def test_api_server(base_url="http://127.0.0.1:8000"):
    """测试API服务器功能"""
    
    print(f"测试API服务器: {base_url}")
    print("=" * 50)
    
    try:
        # 测试健康检查
        print("1. 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
        
        # 测试根路径
        print("\n2. 测试根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 根路径访问成功")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 根路径访问失败: {response.status_code}")
        
        # 测试模型列表
        print("\n3. 测试模型列表...")
        response = requests.get(f"{base_url}/models/list", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 模型列表获取成功")
            print(f"   找到 {data.get('count', 0)} 个模型")
            if data.get('models'):
                for model in data['models'][:3]:  # 只显示前3个
                    print(f"   - {model.get('name', 'Unknown')}")
        else:
            print(f"❌ 模型列表获取失败: {response.status_code}")
        
        # 测试配置列表
        print("\n4. 测试配置列表...")
        response = requests.get(f"{base_url}/models/configs", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置列表获取成功")
            print(f"   找到 {data.get('count', 0)} 个配置文件")
            if data.get('configs'):
                for config in data['configs'][:3]:  # 只显示前3个
                    print(f"   - {config.get('name', 'Unknown')}")
        else:
            print(f"❌ 配置列表获取失败: {response.status_code}")
        
        # 测试任务列表
        print("\n5. 测试任务列表...")
        response = requests.get(f"{base_url}/tasks", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 任务列表获取成功")
            print(f"   当前有 {data.get('count', 0)} 个任务")
        else:
            print(f"❌ 任务列表获取失败: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("✅ API服务器测试完成！")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("   请确保API服务器正在运行:")
        print("   python api_server.py")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    # 测试本地API服务器
    success = test_api_server()
    
    if not success:
        print("\n提示: 如果要启动API服务器，请运行:")
        print("python api_server.py --host 127.0.0.1 --port 8000")
