#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频工具函数 - 提供音频处理相关的实用函数
"""

import logging
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, List

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False


class AudioUtils:
    """音频工具类"""
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """
        格式化时长显示
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时长字符串 (mm:ss 或 hh:mm:ss)
        """
        if seconds < 0:
            return "0:00"
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes}:{secs:02d}"
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化的文件大小字符串
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    @staticmethod
    def get_audio_format_info(file_path: str) -> dict:
        """
        获取音频格式信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            dict: 格式信息字典
        """
        file_path_obj = Path(file_path)
        extension = file_path_obj.suffix.lower()
        
        format_info = {
            '.wav': {
                'name': 'WAV',
                'description': '无损音频格式',
                'quality': 'Lossless',
                'typical_bitrate': 'N/A'
            },
            '.mp3': {
                'name': 'MP3',
                'description': '有损压缩音频格式',
                'quality': 'Lossy',
                'typical_bitrate': '128-320 kbps'
            },
            '.flac': {
                'name': 'FLAC',
                'description': '无损压缩音频格式',
                'quality': 'Lossless',
                'typical_bitrate': 'Variable'
            },
            '.m4a': {
                'name': 'M4A',
                'description': 'AAC音频格式',
                'quality': 'Lossy',
                'typical_bitrate': '128-256 kbps'
            },
            '.aac': {
                'name': 'AAC',
                'description': '高级音频编码',
                'quality': 'Lossy',
                'typical_bitrate': '128-256 kbps'
            },
            '.ogg': {
                'name': 'OGG',
                'description': 'Ogg Vorbis音频格式',
                'quality': 'Lossy',
                'typical_bitrate': '128-320 kbps'
            }
        }
        
        return format_info.get(extension, {
            'name': extension.upper(),
            'description': '未知音频格式',
            'quality': 'Unknown',
            'typical_bitrate': 'Unknown'
        })
    
    @staticmethod
    def is_supported_format(file_path: str) -> bool:
        """
        检查是否为支持的音频格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 支持返回True
        """
        supported_extensions = {'.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg'}
        extension = Path(file_path).suffix.lower()
        return extension in supported_extensions
    
    @staticmethod
    def calculate_bitrate(file_size: int, duration: float) -> Optional[int]:
        """
        计算音频比特率
        
        Args:
            file_size: 文件大小（字节）
            duration: 时长（秒）
            
        Returns:
            int: 比特率（kbps），计算失败返回None
        """
        try:
            if duration <= 0:
                return None
            
            # 比特率 = (文件大小 * 8) / 时长 / 1000
            bitrate = (file_size * 8) / duration / 1000
            return int(bitrate)
            
        except Exception:
            return None
    
    @staticmethod
    def add_reverb_effect(
        audio_data: np.ndarray, 
        sample_rate: int,
        room_size: float = 0.3,
        damping: float = 0.1,
        wet_level: float = 0.2,
        dry_level: float = 0.9
    ) -> np.ndarray:
        """
        添加混响效果
        
        Args:
            audio_data: 音频数据数组
            sample_rate: 采样率
            room_size: 房间大小 (0.0-1.0)
            damping: 阻尼 (0.0-1.0)
            wet_level: 湿声电平 (0.0-1.0)
            dry_level: 干声电平 (0.0-1.0)
            
        Returns:
            np.ndarray: 处理后的音频数据
        """
        try:
            # 确保输入是浮点数组
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 创建延迟参数
            delay_samples = int(room_size * sample_rate * 0.2)  # 最大延迟0.2秒
            decay = 1.0 - damping  # 衰减因子
            
            # 初始化混响信号
            reverb_signal = np.zeros_like(audio_data)
            
            # 创建多个延迟，模拟多次反射
            for i in range(1, 5):
                delay = int(delay_samples * i)
                if delay >= len(audio_data):
                    continue
                
                amplitude = decay ** i
                
                # 处理单声道或立体声
                if len(audio_data.shape) == 1:
                    # 单声道
                    reverb_signal[delay:] += audio_data[:-delay] * amplitude * wet_level
                else:
                    # 立体声
                    reverb_signal[delay:, :] += audio_data[:-delay, :] * amplitude * wet_level
            
            # 混合原始信号和混响信号
            output = audio_data * dry_level + reverb_signal
            
            # 防止削波
            max_val = np.max(np.abs(output))
            if max_val > 1.0:
                output = output / max_val
            
            return output
            
        except Exception as e:
            logging.error(f"添加混响效果失败: {e}")
            return audio_data
    
    @staticmethod
    def normalize_audio(audio_data: np.ndarray, target_level: float = 0.8) -> np.ndarray:
        """
        标准化音频电平
        
        Args:
            audio_data: 音频数据数组
            target_level: 目标电平 (0.0-1.0)
            
        Returns:
            np.ndarray: 标准化后的音频数据
        """
        try:
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                return audio_data * (target_level / max_val)
            return audio_data
            
        except Exception as e:
            logging.error(f"音频标准化失败: {e}")
            return audio_data
    
    @staticmethod
    def apply_fade(
        audio_data: np.ndarray, 
        fade_in_duration: float = 0.0,
        fade_out_duration: float = 0.0,
        sample_rate: int = 44100
    ) -> np.ndarray:
        """
        应用淡入淡出效果
        
        Args:
            audio_data: 音频数据数组
            fade_in_duration: 淡入时长（秒）
            fade_out_duration: 淡出时长（秒）
            sample_rate: 采样率
            
        Returns:
            np.ndarray: 处理后的音频数据
        """
        try:
            output = audio_data.copy()
            
            # 淡入
            if fade_in_duration > 0:
                fade_in_samples = int(fade_in_duration * sample_rate)
                fade_in_samples = min(fade_in_samples, len(output))
                
                fade_curve = np.linspace(0, 1, fade_in_samples)
                
                if len(output.shape) == 1:
                    output[:fade_in_samples] *= fade_curve
                else:
                    output[:fade_in_samples, :] *= fade_curve[:, np.newaxis]
            
            # 淡出
            if fade_out_duration > 0:
                fade_out_samples = int(fade_out_duration * sample_rate)
                fade_out_samples = min(fade_out_samples, len(output))
                
                fade_curve = np.linspace(1, 0, fade_out_samples)
                
                if len(output.shape) == 1:
                    output[-fade_out_samples:] *= fade_curve
                else:
                    output[-fade_out_samples:, :] *= fade_curve[:, np.newaxis]
            
            return output
            
        except Exception as e:
            logging.error(f"应用淡入淡出失败: {e}")
            return audio_data
    
    @staticmethod
    def detect_silence(
        audio_data: np.ndarray,
        threshold: float = 0.01,
        min_duration: float = 0.5,
        sample_rate: int = 44100
    ) -> List[Tuple[float, float]]:
        """
        检测静音段
        
        Args:
            audio_data: 音频数据数组
            threshold: 静音阈值
            min_duration: 最小静音时长（秒）
            sample_rate: 采样率
            
        Returns:
            List[Tuple[float, float]]: 静音段列表 [(开始时间, 结束时间), ...]
        """
        try:
            # 计算音频能量
            if len(audio_data.shape) > 1:
                # 立体声转单声道
                energy = np.mean(np.abs(audio_data), axis=1)
            else:
                energy = np.abs(audio_data)
            
            # 检测低于阈值的区域
            is_silent = energy < threshold
            
            # 查找静音段
            silent_regions = []
            in_silence = False
            start_idx = 0
            
            for i, silent in enumerate(is_silent):
                if silent and not in_silence:
                    # 静音开始
                    start_idx = i
                    in_silence = True
                elif not silent and in_silence:
                    # 静音结束
                    duration = (i - start_idx) / sample_rate
                    if duration >= min_duration:
                        start_time = start_idx / sample_rate
                        end_time = i / sample_rate
                        silent_regions.append((start_time, end_time))
                    in_silence = False
            
            # 处理结尾的静音
            if in_silence:
                duration = (len(is_silent) - start_idx) / sample_rate
                if duration >= min_duration:
                    start_time = start_idx / sample_rate
                    end_time = len(is_silent) / sample_rate
                    silent_regions.append((start_time, end_time))
            
            return silent_regions
            
        except Exception as e:
            logging.error(f"检测静音段失败: {e}")
            return []
