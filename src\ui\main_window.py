#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口类 - 应用程序的主界面窗口
重新设计为单页面布局，采用HTML布局的三栏式设计
"""

import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QScrollArea, QSplitter, QStatusBar,
    QComboBox, QSlider, QCheckBox, QSpinBox, QPushButton,
    QFileDialog, QProgressBar, QTextEdit, QMessageBox
)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QAction, QIcon, QCloseEvent, QFont, QPixmap

from core.config_manager import ConfigManager
from ui.components.collapsible_section import CollapsibleSection, CollapsibleCard


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    config_changed = Signal()
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """
        初始化主窗口
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 获取UI配置
        self.ui_config = config_manager.get_ui_config()
        
        # 初始化界面
        self._setup_window()
        self._create_menu_bar()
        self._create_status_bar()
        self._create_central_widget()
        self._apply_theme()
        self._connect_signals()
        
        self.logger.info("主窗口初始化完成")
    
    def _setup_window(self):
        """设置窗口基本属性"""
        self.setWindowTitle("AI翻唱")
        self.setMinimumSize(QSize(1200, 800))

        # 设置窗口大小
        self.resize(1400, 900)

        # 设置窗口图标
        icon_path = Path(__file__).parent.parent.parent / "assets" / "images" / "title.ico"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))

        # 居中显示窗口
        self._center_window()
    
    def _center_window(self):
        """将窗口居中显示"""
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件动作
        open_action = QAction("打开音频文件(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self._on_open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # API服务管理
        api_action = QAction("API服务管理(&A)", self)
        api_action.triggered.connect(self._on_api_management)
        tools_menu.addAction(api_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于动作
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def _create_central_widget(self):
        """创建中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 采用网格布局实现三栏式设计
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(24)

        # 创建左侧栏
        self._create_left_panel(main_layout)

        # 创建右侧主内容区域
        self._create_right_panel(main_layout)
    
    def _create_left_panel(self, parent_layout):
        """创建左侧面板"""
        # 左侧容器
        left_widget = QWidget()
        left_widget.setFixedWidth(400)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(24)

        # Logo区域
        self._create_logo_section(left_layout)

        # 折叠式菜单区域
        self._create_collapsible_sections(left_layout)

        # 上传和处理区域
        self._create_upload_section(left_layout)

        # 添加弹性空间
        left_layout.addStretch()

        parent_layout.addWidget(left_widget)

    def _create_right_panel(self, parent_layout):
        """创建右侧主内容面板"""
        # 右侧容器
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)

        # 创建主要参数配置区域
        self._create_main_config_section(right_layout)

        parent_layout.addWidget(right_widget)

    def _create_logo_section(self, parent_layout):
        """创建Logo区域"""
        # Logo容器
        logo_container = QWidget()
        logo_layout = QHBoxLayout(logo_container)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo_layout.setAlignment(Qt.AlignCenter)

        # Logo图片
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)

        # 尝试加载Logo图片
        logo_path = Path(__file__).parent.parent.parent / "0-other" / "logo.svg"
        if logo_path.exists():
            pixmap = QPixmap(str(logo_path))
            if not pixmap.isNull():
                # 缩放Logo到合适大小
                scaled_pixmap = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                # 如果图片加载失败，显示文字
                logo_label.setText("木偶AI")
                logo_label.setFont(QFont("Microsoft YaHei UI", 18, QFont.Bold))
                logo_label.setStyleSheet("color: #E5E7EB;")
        else:
            # 如果没有图片文件，显示文字
            logo_label.setText("木偶AI")
            logo_label.setFont(QFont("Microsoft YaHei UI", 18, QFont.Bold))
            logo_label.setStyleSheet("color: #E5E7EB;")

        logo_layout.addWidget(logo_label)
        parent_layout.addWidget(logo_container)

    def _apply_theme(self):
        """应用主题样式"""
        # 深色主题样式
        dark_style = """
        QMainWindow {
            background-color: #161B22;
            color: #E6EDF3;
        }
        
        QTabWidget::pane {
            border: 1px solid #30363D;
            background-color: #21262D;
        }
        
        QTabWidget::tab-bar {
            alignment: left;
        }
        
        QTabBar::tab {
            background-color: #1C2128;
            color: #8B949E;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: #21262D;
            color: #E6EDF3;
            border-bottom: 2px solid #58A6FF;
        }
        
        QTabBar::tab:hover:!selected {
            background-color: #30363D;
            color: #E6EDF3;
        }
        
        QStatusBar {
            background-color: #1C2128;
            color: #8B949E;
            border-top: 1px solid #30363D;
        }
        
        QMenuBar {
            background-color: #1C2128;
            color: #E6EDF3;
            border-bottom: 1px solid #30363D;
        }
        
        QMenuBar::item {
            padding: 4px 8px;
            background-color: transparent;
        }
        
        QMenuBar::item:selected {
            background-color: #30363D;
        }
        
        QMenu {
            background-color: #21262D;
            color: #E6EDF3;
            border: 1px solid #30363D;
        }
        
        QMenu::item {
            padding: 6px 20px;
        }
        
        QMenu::item:selected {
            background-color: #58A6FF;
        }
        """
        
        self.setStyleSheet(dark_style)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        
        # 连接配置变更信号
        self.config_changed.connect(self._on_config_changed)
    
    def _on_tab_changed(self, index: int):
        """选项卡切换处理"""
        tab_names = ["歌曲管理", "翻唱处理", "API管理", "设置"]
        if 0 <= index < len(tab_names):
            self.status_bar.showMessage(f"当前选项卡: {tab_names[index]}")
    
    def _on_open_file(self):
        """打开文件处理"""
        # 切换到翻唱处理选项卡并触发文件选择
        self.tab_widget.setCurrentIndex(1)
        self.audio_processor_tab.open_file_dialog()
    
    def _on_api_management(self):
        """API管理处理"""
        # 切换到API管理选项卡
        self.tab_widget.setCurrentIndex(2)
    
    def _on_about(self):
        """关于对话框"""
        QMessageBox.about(
            self,
            "关于木偶AI翻唱",
            "木偶AI翻唱 v2.0\n\n"
            "基于PySide6的模块化AI翻唱应用程序\n"
            "支持本地和云端音频处理\n\n"
            "© 2024 MuOu Studio"
        )
    
    def _on_config_changed(self):
        """配置变更处理"""
        self.logger.info("配置已更新")
    
    def closeEvent(self, event: QCloseEvent):
        """窗口关闭事件处理"""
        # 保存窗口状态
        if self.ui_config.remember_window_state:
            self.ui_config.window_width = self.width()
            self.ui_config.window_height = self.height()
            self.config_manager.save_config()
        
        self.logger.info("主窗口关闭")
        event.accept()
    
    def show_status_message(self, message: str, timeout: int = 0):
        """显示状态栏消息"""
        self.status_bar.showMessage(message, timeout)
