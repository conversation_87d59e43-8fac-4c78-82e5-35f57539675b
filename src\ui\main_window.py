#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口类 - 应用程序的主界面窗口
完全按照HTML布局重新设计的单页面布局
"""

import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QScrollArea, QStatusBar, QLineEdit,
    QComboBox, QSlider, QCheckBox, QSpinBox, QPushButton,
    QFileDialog, QProgressBar, QTextEdit, QMessageBox, QSizePolicy
)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QAction, QIcon, QCloseEvent, QFont, QPixmap

from core.config_manager import ConfigManager
from ui.components.collapsible_section import CollapsibleSection, CollapsibleCard


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    config_changed = Signal()
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """
        初始化主窗口
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 获取UI配置
        self.ui_config = config_manager.get_ui_config()
        
        # 初始化界面
        self._setup_window()
        self._create_menu_bar()
        self._create_status_bar()
        self._create_central_widget()
        self._apply_theme()
        self._connect_signals()
        
        self.logger.info("主窗口初始化完成")
    
    def _setup_window(self):
        """设置窗口基本属性"""
        self.setWindowTitle("AI翻唱")
        self.setMinimumSize(QSize(1200, 800))

        # 设置窗口大小
        self.resize(1400, 900)

        # 设置窗口图标
        icon_path = Path(__file__).parent.parent.parent / "assets" / "images" / "title.ico"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))

        # 居中显示窗口
        self._center_window()
    
    def _center_window(self):
        """将窗口居中显示"""
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件动作
        open_action = QAction("打开音频文件(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self._on_open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # API服务管理
        api_action = QAction("API服务管理(&A)", self)
        api_action.triggered.connect(self._on_api_management)
        tools_menu.addAction(api_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于动作
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def _create_central_widget(self):
        """创建中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平布局实现左右两栏设计
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(32, 32, 32, 32)  # 对应HTML的p-8
        main_layout.setSpacing(32)  # 对应HTML的gap-8

        # 创建左侧栏
        self._create_left_panel(main_layout)

        # 创建右侧主内容区域
        self._create_right_panel(main_layout)
    
    def _create_left_panel(self, parent_layout):
        """创建左侧面板"""
        # 左侧容器 - 对应HTML的lg:col-span-1
        left_widget = QWidget()
        left_widget.setFixedWidth(400)  # 固定宽度
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(32)  # 对应HTML的space-y-8

        # Logo区域
        self._create_logo_section(left_layout)

        # 折叠式菜单区域
        self._create_collapsible_sections(left_layout)

        # 上传和处理区域
        self._create_upload_section(left_layout)

        # 添加弹性空间
        left_layout.addStretch()

        parent_layout.addWidget(left_widget)

    def _create_right_panel(self, parent_layout):
        """创建右侧主内容面板"""
        # 右侧容器
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)

        # 创建主要参数配置区域
        self._create_main_config_section(right_layout)

        parent_layout.addWidget(right_widget)

    def _create_logo_section(self, parent_layout):
        """创建Logo区域 - 对应HTML中的Logo部分"""
        # Logo容器 - 居中对齐
        logo_container = QWidget()
        logo_layout = QHBoxLayout(logo_container)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo_layout.setAlignment(Qt.AlignCenter)

        # Logo图片
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)

        # 尝试加载Logo图片 - 使用HTML中的图片URL作为参考
        # HTML中使用的是网络图片，这里使用本地图片
        logo_path = Path(__file__).parent.parent.parent / "0-other" / "logo.png"
        if logo_path.exists():
            pixmap = QPixmap(str(logo_path))
            if not pixmap.isNull():
                # 缩放Logo到HTML中的大小 h-16 (64px)
                scaled_pixmap = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                self._set_logo_text(logo_label)
        else:
            self._set_logo_text(logo_label)

        logo_layout.addWidget(logo_label)
        parent_layout.addWidget(logo_container)

    def _set_logo_text(self, logo_label):
        """设置Logo文字"""
        logo_label.setText("木偶AI")
        logo_label.setFont(QFont("Microsoft YaHei UI", 18, QFont.Weight.Bold))
        logo_label.setStyleSheet("color: #E5E7EB;")

    def _create_collapsible_sections(self, parent_layout):
        """创建折叠式菜单区域 - 对应HTML中的Accordion Sections"""
        sections_container = QWidget()
        sections_layout = QVBoxLayout(sections_container)
        sections_layout.setContentsMargins(0, 0, 0, 0)
        sections_layout.setSpacing(16)  # 对应HTML的space-y-4

        # 歌曲管理折叠区域
        song_section = CollapsibleSection("歌曲管理", "music_note")
        song_card = CollapsibleCard()

        # 我的成品区域
        song_title = QLabel("我的成品")
        song_title.setStyleSheet("""
            color: #9CA3AF;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 24px;
            border-bottom: 1px solid #374151;
            padding-bottom: 12px;
        """)
        song_card.add_widget(song_title)

        # 空状态显示
        empty_state = QWidget()
        empty_layout = QVBoxLayout(empty_state)
        empty_layout.setAlignment(Qt.AlignCenter)
        empty_layout.setContentsMargins(0, 40, 0, 40)

        empty_icon = QLabel("🎵")
        empty_icon.setAlignment(Qt.AlignCenter)
        empty_icon.setStyleSheet("font-size: 48px; color: #6B7280; margin-bottom: 8px;")

        empty_text = QLabel("暂无成品歌曲")
        empty_text.setAlignment(Qt.AlignCenter)
        empty_text.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        empty_layout.addWidget(empty_icon)
        empty_layout.addWidget(empty_text)
        song_card.add_widget(empty_state)

        song_section.add_content_widget(song_card)
        sections_layout.addWidget(song_section)

        # API管理折叠区域
        api_section = CollapsibleSection("API管理", "api")
        api_card = CollapsibleCard()
        self._create_api_config_content(api_card)
        api_section.add_content_widget(api_card)
        sections_layout.addWidget(api_section)

        parent_layout.addWidget(sections_container)

    def _create_api_config_content(self, parent_card):
        """创建API配置内容"""
        # API配置标题
        api_title = QLabel("API 配置")
        api_title.setStyleSheet("""
            color: #9CA3AF;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 24px;
            border-bottom: 1px solid #374151;
            padding-bottom: 12px;
        """)
        parent_card.add_widget(api_title)

        # API URL输入
        url_container = QWidget()
        url_layout = QVBoxLayout(url_container)
        url_layout.setContentsMargins(0, 0, 0, 0)
        url_layout.setSpacing(8)

        url_label = QLabel("API URL:")
        url_label.setStyleSheet("color: #9CA3AF; font-size: 14px; margin-bottom: 8px;")

        self.api_url_input = QLineEdit("http://127.0.0.1:9880")
        self.api_url_input.setStyleSheet(self._get_input_style())

        url_layout.addWidget(url_label)
        url_layout.addWidget(self.api_url_input)
        parent_card.add_widget(url_container)

        # Python环境输入
        python_container = QWidget()
        python_layout = QVBoxLayout(python_container)
        python_layout.setContentsMargins(0, 0, 0, 0)
        python_layout.setSpacing(8)

        python_label = QLabel("Python 环境:")
        python_label.setStyleSheet("color: #9CA3AF; font-size: 14px; margin-bottom: 8px;")

        python_input_container = QWidget()
        python_input_layout = QHBoxLayout(python_input_container)
        python_input_layout.setContentsMargins(0, 0, 0, 0)
        python_input_layout.setSpacing(8)

        self.python_env_input = QLineEdit("workenv\\python.exe")
        self.python_env_input.setStyleSheet(self._get_input_style())

        browse_btn = QPushButton("浏览")
        browse_btn.setStyleSheet(self._get_secondary_button_style())
        browse_btn.clicked.connect(self._browse_python_env)

        python_input_layout.addWidget(self.python_env_input)
        python_input_layout.addWidget(browse_btn)

        python_layout.addWidget(python_label)
        python_layout.addWidget(python_input_container)
        parent_card.add_widget(python_container)

        # 复选框选项
        checkbox_container = QWidget()
        checkbox_layout = QVBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        checkbox_layout.setSpacing(12)

        self.auto_start_checkbox = QCheckBox("随软件启动API")
        self.auto_start_checkbox.setChecked(True)
        self.auto_start_checkbox.setStyleSheet(self._get_checkbox_style())

        self.cloud_api_checkbox = QCheckBox("云端API")
        self.cloud_api_checkbox.setChecked(True)
        self.cloud_api_checkbox.setStyleSheet(self._get_checkbox_style())

        checkbox_layout.addWidget(self.auto_start_checkbox)
        checkbox_layout.addWidget(self.cloud_api_checkbox)
        parent_card.add_widget(checkbox_container)

        # 控制台输出
        console_title = QLabel("控制台输出")
        console_title.setStyleSheet("color: #D1D5DB; font-size: 18px; font-weight: 600; margin-top: 16px;")
        parent_card.add_widget(console_title)

        self.console_output = QTextEdit()
        self.console_output.setFixedHeight(200)
        self.console_output.setStyleSheet("""
            QTextEdit {
                background-color: #111827;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 16px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                color: #9CA3AF;
            }
        """)
        self.console_output.setPlainText("> Console output will appear here...")
        parent_card.add_widget(self.console_output)

        # API控制按钮
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(16)

        self.start_api_btn = QPushButton("启动 API")
        self.start_api_btn.setStyleSheet(self._get_primary_button_style())
        self.start_api_btn.clicked.connect(self._start_api)

        self.stop_api_btn = QPushButton("停止 API")
        self.stop_api_btn.setStyleSheet(self._get_secondary_button_style())
        self.stop_api_btn.clicked.connect(self._stop_api)

        button_layout.addWidget(self.start_api_btn)
        button_layout.addWidget(self.stop_api_btn)
        parent_card.add_widget(button_container)

    def _apply_theme(self):
        """应用主题样式 - 完全按照HTML布局的配色方案"""
        # 深色主题样式 - 对应HTML中的配色
        dark_style = f"""
        QMainWindow {{
            background-color: #111827;  /* 对应HTML body background */
            color: #E5E7EB;
        }}

        QStatusBar {{
            background-color: #1F2937;
            color: #9CA3AF;
            border-top: 1px solid #374151;
        }}

        QMenuBar {{
            background-color: #1F2937;
            color: #E5E7EB;
            border-bottom: 1px solid #374151;
        }}

        QMenuBar::item {{
            padding: 8px 12px;
            background-color: transparent;
        }}

        QMenuBar::item:selected {{
            background-color: #374151;
        }}

        QMenu {{
            background-color: #1F2937;
            color: #E5E7EB;
            border: 1px solid #374151;
        }}

        QMenu::item {{
            padding: 8px 20px;
        }}

        QMenu::item:selected {{
            background-color: #3B82F6;
        }}
        """

        self.setStyleSheet(dark_style)

    def _get_input_style(self):
        """获取输入框样式 - 对应HTML的input-field类"""
        return """
            QLineEdit {
                background-color: #374151;
                border: 1px solid #4B5563;
                border-radius: 8px;
                padding: 10px 12px;
                color: #E5E7EB;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        """

    def _get_primary_button_style(self):
        """获取主要按钮样式 - 对应HTML的btn-primary类"""
        return """
            QPushButton {
                background-color: #3B82F6;
                color: white;
                font-weight: 600;
                padding: 12px 24px;
                border-radius: 8px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """

    def _get_secondary_button_style(self):
        """获取次要按钮样式 - 对应HTML的btn-secondary类"""
        return """
            QPushButton {
                background-color: #374151;
                color: #E5E7EB;
                font-weight: 500;
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:pressed {
                background-color: #6B7280;
            }
        """

    def _get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                color: #D1D5DB;
                font-size: 14px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 1px solid #6B7280;
                background-color: #4B5563;
            }
            QCheckBox::indicator:checked {
                background-color: #3B82F6;
                border-color: #3B82F6;
            }
        """

    def _create_upload_section(self, parent_layout):
        """创建上传和处理区域 - 对应HTML中的Upload and Process部分"""
        upload_card = CollapsibleCard()

        # 文件拖拽区域
        drop_area = QWidget()
        drop_area.setFixedHeight(120)
        drop_area.setStyleSheet("""
            QWidget {
                border: 2px dashed #3B82F6;
                border-radius: 8px;
                background-color: rgba(55, 65, 81, 0.3);
            }
            QWidget:hover {
                background-color: rgba(75, 85, 99, 0.3);
            }
        """)

        drop_layout = QVBoxLayout(drop_area)
        drop_layout.setAlignment(Qt.AlignCenter)

        upload_icon = QLabel("📁")
        upload_icon.setAlignment(Qt.AlignCenter)
        upload_icon.setStyleSheet("font-size: 48px; color: #60A5FA; margin-bottom: 12px;")

        upload_text = QLabel("在此处选择或拖拽音频文件")
        upload_text.setAlignment(Qt.AlignCenter)
        upload_text.setStyleSheet("color: #93C5FD; font-size: 14px;")

        drop_layout.addWidget(upload_icon)
        drop_layout.addWidget(upload_text)
        upload_card.add_widget(drop_area)

        # 处理选项
        options_container = QWidget()
        options_layout = QHBoxLayout(options_container)
        options_layout.setContentsMargins(0, 16, 0, 0)
        options_layout.setSpacing(16)

        # 处理模式
        mode_container = QWidget()
        mode_layout = QVBoxLayout(mode_container)
        mode_layout.setContentsMargins(0, 0, 0, 0)
        mode_layout.setSpacing(8)

        mode_label = QLabel("处理模式:")
        mode_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        self.processing_mode = QComboBox()
        self.processing_mode.addItems(["完整模式", "快速模式"])
        self.processing_mode.setStyleSheet(self._get_combobox_style())

        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.processing_mode)

        # 输出格式
        format_container = QWidget()
        format_layout = QVBoxLayout(format_container)
        format_layout.setContentsMargins(0, 0, 0, 0)
        format_layout.setSpacing(8)

        format_label = QLabel("输出格式:")
        format_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        self.output_format = QComboBox()
        self.output_format.addItems(["WAV", "MP3"])
        self.output_format.setStyleSheet(self._get_combobox_style())

        format_layout.addWidget(format_label)
        format_layout.addWidget(self.output_format)

        options_layout.addWidget(mode_container)
        options_layout.addWidget(format_container)
        upload_card.add_widget(options_container)

        # 一键翻唱按钮
        process_container = QWidget()
        process_layout = QVBoxLayout(process_container)
        process_layout.setContentsMargins(0, 32, 0, 0)
        process_layout.setSpacing(16)

        self.process_btn = QPushButton("🚀 一键翻唱")
        self.process_btn.setStyleSheet(f"""
            {self._get_primary_button_style()}
            QPushButton {{
                font-size: 18px;
                padding: 16px 32px;
            }}
        """)
        self.process_btn.clicked.connect(self._start_processing)

        status_label = QLabel("状态: <span style='font-weight: 600; color: #D1D5DB;'>空闲</span>")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        process_layout.addWidget(self.process_btn)
        process_layout.addWidget(status_label)
        upload_card.add_widget(process_container)

        parent_layout.addWidget(upload_card)

    def _get_combobox_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                background-color: #374151;
                border: 1px solid #4B5563;
                border-radius: 8px;
                padding: 10px 12px;
                color: #E5E7EB;
                font-size: 14px;
            }
            QComboBox:focus {
                border-color: #3B82F6;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #9CA3AF;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #374151;
                border: 1px solid #4B5563;
                color: #E5E7EB;
                selection-background-color: #3B82F6;
            }
        """

    def _create_right_panel(self, parent_layout):
        """创建右侧主内容面板 - 对应HTML的lg:col-span-2"""
        # 右侧容器
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(32)  # 对应HTML的space-y-8

        # 创建主要参数配置区域
        self._create_main_config_section(right_layout)

        parent_layout.addWidget(right_widget)

    def _create_main_config_section(self, parent_layout):
        """创建主要配置区域 - 对应HTML中的右侧配置面板"""
        config_card = CollapsibleCard()

        # 模型与音高配置
        self._create_model_pitch_section(config_card)

        # 混响与和声配置
        self._create_reverb_harmony_section(config_card)

        # 高级参数配置
        self._create_advanced_params_section(config_card)

        parent_layout.addWidget(config_card)

    def _create_model_pitch_section(self, parent_card):
        """创建模型与音高配置区域"""
        # 标题
        title = QLabel("模型与音高")
        title.setStyleSheet("""
            color: #D1D5DB;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        """)
        parent_card.add_widget(title)

        # 配置容器
        config_container = QWidget()
        config_layout = QHBoxLayout(config_container)
        config_layout.setContentsMargins(0, 0, 0, 0)
        config_layout.setSpacing(24)

        # 左列
        left_column = QWidget()
        left_layout = QVBoxLayout(left_column)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(16)

        # 音色模型
        model_container = QWidget()
        model_layout = QVBoxLayout(model_container)
        model_layout.setContentsMargins(0, 0, 0, 0)
        model_layout.setSpacing(8)

        model_label = QLabel("音色模型 (pt):")
        model_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        self.voice_model_input = QLineEdit("YSML.pt")
        self.voice_model_input.setStyleSheet(self._get_input_style())

        model_layout.addWidget(model_label)
        model_layout.addWidget(self.voice_model_input)

        # 人声音高
        human_pitch_container = QWidget()
        human_pitch_layout = QVBoxLayout(human_pitch_container)
        human_pitch_layout.setContentsMargins(0, 0, 0, 0)
        human_pitch_layout.setSpacing(8)

        human_pitch_label = QLabel("人声音高:")
        human_pitch_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        human_pitch_control = QWidget()
        human_pitch_control_layout = QHBoxLayout(human_pitch_control)
        human_pitch_control_layout.setContentsMargins(0, 0, 0, 0)
        human_pitch_control_layout.setSpacing(12)

        self.human_pitch_slider = QSlider(Qt.Horizontal)
        self.human_pitch_slider.setRange(-12, 12)
        self.human_pitch_slider.setValue(0)
        self.human_pitch_slider.setStyleSheet(self._get_slider_style())

        self.human_pitch_value = QLabel("0")
        self.human_pitch_value.setFixedWidth(32)
        self.human_pitch_value.setAlignment(Qt.AlignRight)
        self.human_pitch_value.setStyleSheet("color: #D1D5DB; font-size: 14px;")

        self.human_pitch_slider.valueChanged.connect(
            lambda v: self.human_pitch_value.setText(str(v))
        )

        human_pitch_control_layout.addWidget(self.human_pitch_slider)
        human_pitch_control_layout.addWidget(self.human_pitch_value)

        human_pitch_layout.addWidget(human_pitch_label)
        human_pitch_layout.addWidget(human_pitch_control)

        left_layout.addWidget(model_container)
        left_layout.addWidget(human_pitch_container)

        # 右列
        right_column = QWidget()
        right_layout = QVBoxLayout(right_column)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(16)

        # 配置文件
        config_container_right = QWidget()
        config_layout_right = QVBoxLayout(config_container_right)
        config_layout_right.setContentsMargins(0, 0, 0, 0)
        config_layout_right.setSpacing(8)

        config_label = QLabel("配置文件 (yaml):")
        config_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        self.config_file_input = QLineEdit("YSML.yaml")
        self.config_file_input.setStyleSheet(self._get_input_style())

        config_layout_right.addWidget(config_label)
        config_layout_right.addWidget(self.config_file_input)

        # 伴奏音高
        instrumental_pitch_container = QWidget()
        instrumental_pitch_layout = QVBoxLayout(instrumental_pitch_container)
        instrumental_pitch_layout.setContentsMargins(0, 0, 0, 0)
        instrumental_pitch_layout.setSpacing(8)

        instrumental_pitch_label = QLabel("伴奏音高:")
        instrumental_pitch_label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        instrumental_pitch_control = QWidget()
        instrumental_pitch_control_layout = QHBoxLayout(instrumental_pitch_control)
        instrumental_pitch_control_layout.setContentsMargins(0, 0, 0, 0)
        instrumental_pitch_control_layout.setSpacing(12)

        self.instrumental_pitch_slider = QSlider(Qt.Horizontal)
        self.instrumental_pitch_slider.setRange(-12, 12)
        self.instrumental_pitch_slider.setValue(0)
        self.instrumental_pitch_slider.setStyleSheet(self._get_slider_style())

        self.instrumental_pitch_value = QLabel("0")
        self.instrumental_pitch_value.setFixedWidth(32)
        self.instrumental_pitch_value.setAlignment(Qt.AlignRight)
        self.instrumental_pitch_value.setStyleSheet("color: #D1D5DB; font-size: 14px;")

        self.instrumental_pitch_slider.valueChanged.connect(
            lambda v: self.instrumental_pitch_value.setText(str(v))
        )

        instrumental_pitch_control_layout.addWidget(self.instrumental_pitch_slider)
        instrumental_pitch_control_layout.addWidget(self.instrumental_pitch_value)

        instrumental_pitch_layout.addWidget(instrumental_pitch_label)
        instrumental_pitch_layout.addWidget(instrumental_pitch_control)

        right_layout.addWidget(config_container_right)
        right_layout.addWidget(instrumental_pitch_container)

        config_layout.addWidget(left_column)
        config_layout.addWidget(right_column)
        parent_card.add_widget(config_container)

    def _get_slider_style(self):
        """获取滑块样式 - 对应HTML的slider样式"""
        return """
            QSlider::groove:horizontal {
                height: 6px;
                background-color: #4B5563;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                width: 16px;
                height: 16px;
                background-color: #3B82F6;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::handle:horizontal:hover {
                background-color: #2563EB;
            }
        """

    def _create_reverb_harmony_section(self, parent_card):
        """创建混响与和声配置区域"""
        # 分隔线
        separator = QWidget()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #374151; margin: 32px 0;")
        parent_card.add_widget(separator)

        # 标题
        title = QLabel("混响与和声")
        title.setStyleSheet("""
            color: #D1D5DB;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        """)
        parent_card.add_widget(title)

        # 复选框选项
        checkbox_container = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        checkbox_layout.setSpacing(24)

        self.enable_reverb_checkbox = QCheckBox("启用混响")
        self.enable_reverb_checkbox.setStyleSheet(self._get_checkbox_style())

        self.harmony_to_accompaniment_checkbox = QCheckBox("和声加入伴奏")
        self.harmony_to_accompaniment_checkbox.setStyleSheet(self._get_checkbox_style())

        checkbox_layout.addWidget(self.enable_reverb_checkbox)
        checkbox_layout.addWidget(self.harmony_to_accompaniment_checkbox)
        checkbox_layout.addStretch()
        parent_card.add_widget(checkbox_container)

        # 滑块配置
        sliders_container = QWidget()
        sliders_layout = QHBoxLayout(sliders_container)
        sliders_layout.setContentsMargins(0, 16, 0, 0)
        sliders_layout.setSpacing(24)

        # 左列滑块
        left_sliders = QWidget()
        left_sliders_layout = QVBoxLayout(left_sliders)
        left_sliders_layout.setContentsMargins(0, 0, 0, 0)
        left_sliders_layout.setSpacing(16)

        # 房间大小
        room_size_container = self._create_float_slider("房间大小:", 0, 100, 60, "room_size")
        left_sliders_layout.addWidget(room_size_container)

        # 湿润度
        wet_level_container = self._create_float_slider("湿润度:", 0, 100, 30, "wet_level")
        left_sliders_layout.addWidget(wet_level_container)

        # 右列滑块
        right_sliders = QWidget()
        right_sliders_layout = QVBoxLayout(right_sliders)
        right_sliders_layout.setContentsMargins(0, 0, 0, 0)
        right_sliders_layout.setSpacing(16)

        # 阻尼
        damping_container = self._create_float_slider("阻尼:", 0, 100, 10, "reverb_damping")
        right_sliders_layout.addWidget(damping_container)

        # 干燥度
        dry_level_container = self._create_float_slider("干燥度:", 0, 100, 90, "dry_level")
        right_sliders_layout.addWidget(dry_level_container)

        sliders_layout.addWidget(left_sliders)
        sliders_layout.addWidget(right_sliders)
        parent_card.add_widget(sliders_container)

    def _create_float_slider(self, label_text, min_val, max_val, default_val, attr_name):
        """创建浮点数滑块控件"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        label = QLabel(label_text)
        label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        control_container = QWidget()
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(12)

        slider = QSlider(Qt.Horizontal)
        slider.setRange(min_val, max_val)
        slider.setValue(default_val)
        slider.setStyleSheet(self._get_slider_style())

        value_label = QLabel(f"{default_val/100:.2f}")
        value_label.setFixedWidth(40)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet("color: #D1D5DB; font-size: 14px;")

        # 连接滑块值变化
        slider.valueChanged.connect(
            lambda v, lbl=value_label: lbl.setText(f"{v/100:.2f}")
        )

        # 保存滑块引用
        setattr(self, f"{attr_name}_slider", slider)
        setattr(self, f"{attr_name}_value", value_label)

        control_layout.addWidget(slider)
        control_layout.addWidget(value_label)

        layout.addWidget(label)
        layout.addWidget(control_container)

        return container

    def _create_advanced_params_section(self, parent_card):
        """创建高级参数配置区域"""
        # 分隔线
        separator = QWidget()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #374151; margin: 32px 0;")
        parent_card.add_widget(separator)

        # 标题
        title = QLabel("高级参数配置")
        title.setStyleSheet("""
            color: #D1D5DB;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        """)
        parent_card.add_widget(title)

        # 参数配置容器
        params_container = QWidget()
        params_layout = QHBoxLayout(params_container)
        params_layout.setContentsMargins(0, 0, 0, 0)
        params_layout.setSpacing(24)

        # 左列
        left_params = QWidget()
        left_params_layout = QVBoxLayout(left_params)
        left_params_layout.setContentsMargins(0, 0, 0, 0)
        left_params_layout.setSpacing(16)

        # 声码器
        vocoder_container = self._create_combobox_field(
            "声码器:",
            ["pc_nsf_hifigan_testing", "kouon_pc", "nsf_hifigan"],
            "vocoder"
        )
        left_params_layout.addWidget(vocoder_container)

        # 共振峰偏移
        formant_shift_container = self._create_int_slider("共振峰偏移:", -6, 6, 0, "pitch_shift")
        left_params_layout.addWidget(formant_shift_container)

        # 采样器
        sampler_container = self._create_combobox_field(
            "采样器:",
            ["euler", "rk4"],
            "sampler"
        )
        left_params_layout.addWidget(sampler_container)

        # 右列
        right_params = QWidget()
        right_params_layout = QVBoxLayout(right_params)
        right_params_layout.setContentsMargins(0, 0, 0, 0)
        right_params_layout.setSpacing(16)

        # F0提取器
        f0_extractor_container = self._create_combobox_field(
            "F0提取器:",
            ["rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"],
            "f0_extractor"
        )
        right_params_layout.addWidget(f0_extractor_container)

        # 采样步数
        sampling_steps_container = self._create_spinbox_field("采样步数:", 1, 200, 50, "sampling_steps")
        right_params_layout.addWidget(sampling_steps_container)

        # 设备选择
        device_container = self._create_combobox_field(
            "设备选择:",
            ["CUDA (默认)", "CPU"],
            "device_selection"
        )
        right_params_layout.addWidget(device_container)

        params_layout.addWidget(left_params)
        params_layout.addWidget(right_params)
        parent_card.add_widget(params_container)

    def _create_combobox_field(self, label_text, items, attr_name):
        """创建下拉框字段"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        label = QLabel(label_text)
        label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        combobox = QComboBox()
        combobox.addItems(items)
        combobox.setStyleSheet(self._get_combobox_style())

        # 保存下拉框引用
        setattr(self, f"{attr_name}_combo", combobox)

        layout.addWidget(label)
        layout.addWidget(combobox)

        return container

    def _create_int_slider(self, label_text, min_val, max_val, default_val, attr_name):
        """创建整数滑块控件"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        label = QLabel(label_text)
        label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        control_container = QWidget()
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(12)

        slider = QSlider(Qt.Horizontal)
        slider.setRange(min_val, max_val)
        slider.setValue(default_val)
        slider.setStyleSheet(self._get_slider_style())

        value_label = QLabel(str(default_val))
        value_label.setFixedWidth(32)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet("color: #D1D5DB; font-size: 14px;")

        # 连接滑块值变化
        slider.valueChanged.connect(
            lambda v, lbl=value_label: lbl.setText(str(v))
        )

        # 保存滑块引用
        setattr(self, f"{attr_name}_slider", slider)
        setattr(self, f"{attr_name}_value", value_label)

        control_layout.addWidget(slider)
        control_layout.addWidget(value_label)

        layout.addWidget(label)
        layout.addWidget(control_container)

        return container

    def _create_spinbox_field(self, label_text, min_val, max_val, default_val, attr_name):
        """创建数字输入框字段"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        label = QLabel(label_text)
        label.setStyleSheet("color: #9CA3AF; font-size: 14px;")

        spinbox = QSpinBox()
        spinbox.setRange(min_val, max_val)
        spinbox.setValue(default_val)
        spinbox.setStyleSheet("""
            QSpinBox {
                background-color: #374151;
                border: 1px solid #4B5563;
                border-radius: 8px;
                padding: 10px 12px;
                color: #E5E7EB;
                font-size: 14px;
            }
            QSpinBox:focus {
                border-color: #3B82F6;
            }
        """)

        # 保存数字框引用
        setattr(self, f"{attr_name}_spinbox", spinbox)

        layout.addWidget(label)
        layout.addWidget(spinbox)

        return container
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 连接配置变更信号
        self.config_changed.connect(self._on_config_changed)

    def _on_open_file(self):
        """打开文件处理"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.m4a);;所有文件 (*)"
        )
        if file_path:
            self.status_bar.showMessage(f"已选择文件: {Path(file_path).name}")

    def _on_api_management(self):
        """API管理处理"""
        self.status_bar.showMessage("API管理功能")

    def _on_about(self):
        """关于对话框"""
        QMessageBox.about(
            self,
            "关于AI翻唱",
            "AI翻唱 v2.0\n\n"
            "基于PySide6的模块化AI翻唱应用程序\n"
            "支持本地和云端音频处理\n\n"
            "© 2024 MuOu Studio"
        )

    def _on_config_changed(self):
        """配置变更处理"""
        self.logger.info("配置已更新")

    # 事件处理方法
    def _browse_python_env(self):
        """浏览Python环境"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Python可执行文件",
            "",
            "可执行文件 (*.exe);;所有文件 (*)"
        )
        if file_path:
            self.python_env_input.setText(file_path)

    def _start_api(self):
        """启动API"""
        self.console_output.append("> 正在启动API服务...")
        self.status_bar.showMessage("API服务启动中...")
        # TODO: 实现API启动逻辑

    def _stop_api(self):
        """停止API"""
        self.console_output.append("> 正在停止API服务...")
        self.status_bar.showMessage("API服务停止中...")
        # TODO: 实现API停止逻辑

    def _start_processing(self):
        """开始处理"""
        self.console_output.append("> 开始音频处理...")
        self.status_bar.showMessage("正在处理音频...")
        # TODO: 实现音频处理逻辑
    
    def closeEvent(self, event: QCloseEvent):
        """窗口关闭事件处理"""
        # 保存窗口状态
        if self.ui_config.remember_window_state:
            self.ui_config.window_width = self.width()
            self.ui_config.window_height = self.height()
            self.config_manager.save_config()
        
        self.logger.info("主窗口关闭")
        event.accept()
    
    def show_status_message(self, message: str, timeout: int = 0):
        """显示状态栏消息"""
        self.status_bar.showMessage(message, timeout)
