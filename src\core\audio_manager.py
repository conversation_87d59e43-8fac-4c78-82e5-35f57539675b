#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频管理器 - 负责音频文件的处理、转换和管理
"""

import os
import logging
import tempfile
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

try:
    from pydub import AudioSegment
    import numpy as np
    AUDIO_DEPENDENCIES_AVAILABLE = True
except ImportError:
    AUDIO_DEPENDENCIES_AVAILABLE = False


@dataclass
class AudioInfo:
    """音频文件信息数据类"""
    file_path: str
    duration: float  # 秒
    sample_rate: int
    channels: int
    format: str
    file_size: int  # 字节
    bitrate: Optional[int] = None


class AudioManager:
    """音频管理器类"""
    
    def __init__(self, temp_dir: str = "temp"):
        """
        初始化音频管理器
        
        Args:
            temp_dir: 临时文件目录
        """
        self.logger = logging.getLogger(__name__)
        
        # 设置临时目录
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        
        # 支持的音频格式
        self.supported_formats = {'.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg'}
        
        # FFmpeg路径配置
        self.ffmpeg_path = self._find_ffmpeg()
        
        self.logger.info(f"音频管理器初始化完成，临时目录: {self.temp_dir}")
        
        if not AUDIO_DEPENDENCIES_AVAILABLE:
            self.logger.warning("音频处理依赖不可用，部分功能将受限")
    
    def _find_ffmpeg(self) -> str:
        """查找FFmpeg可执行文件"""
        # 首先检查项目目录下的ffmpeg
        local_ffmpeg = Path("ffmpeg") / "bin" / "ffmpeg.exe"
        if local_ffmpeg.exists():
            return str(local_ffmpeg)
        
        # 检查系统PATH中的ffmpeg
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return "ffmpeg"
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        self.logger.warning("未找到FFmpeg，音频转换功能将受限")
        return "ffmpeg"  # 返回默认值，让后续调用时报错
    
    def get_audio_info(self, file_path: str) -> Optional[AudioInfo]:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            AudioInfo: 音频信息对象，失败返回None
        """
        try:
            if not Path(file_path).exists():
                self.logger.error(f"音频文件不存在: {file_path}")
                return None
            
            if not AUDIO_DEPENDENCIES_AVAILABLE:
                # 使用FFmpeg获取信息
                return self._get_audio_info_with_ffmpeg(file_path)
            
            # 使用pydub获取信息
            audio = AudioSegment.from_file(file_path)
            file_stat = Path(file_path).stat()
            
            return AudioInfo(
                file_path=file_path,
                duration=len(audio) / 1000.0,  # 转换为秒
                sample_rate=audio.frame_rate,
                channels=audio.channels,
                format=Path(file_path).suffix.lower(),
                file_size=file_stat.st_size,
                bitrate=getattr(audio, 'bitrate', None)
            )
            
        except Exception as e:
            self.logger.error(f"获取音频信息失败 {file_path}: {e}")
            return None
    
    def _get_audio_info_with_ffmpeg(self, file_path: str) -> Optional[AudioInfo]:
        """使用FFmpeg获取音频信息"""
        try:
            cmd = [
                self.ffmpeg_path, "-i", file_path,
                "-f", "null", "-"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # 解析FFmpeg输出
            output = result.stderr
            
            # 提取基本信息
            duration = 0.0
            sample_rate = 0
            channels = 0
            
            for line in output.split('\n'):
                if 'Duration:' in line:
                    # 解析时长
                    duration_str = line.split('Duration:')[1].split(',')[0].strip()
                    time_parts = duration_str.split(':')
                    if len(time_parts) == 3:
                        hours, minutes, seconds = time_parts
                        duration = float(hours) * 3600 + float(minutes) * 60 + float(seconds)
                
                if 'Audio:' in line:
                    # 解析音频信息
                    parts = line.split(',')
                    for part in parts:
                        part = part.strip()
                        if 'Hz' in part:
                            sample_rate = int(part.split()[0])
                        elif 'mono' in part:
                            channels = 1
                        elif 'stereo' in part:
                            channels = 2
            
            file_stat = Path(file_path).stat()
            
            return AudioInfo(
                file_path=file_path,
                duration=duration,
                sample_rate=sample_rate,
                channels=channels,
                format=Path(file_path).suffix.lower(),
                file_size=file_stat.st_size
            )
            
        except Exception as e:
            self.logger.error(f"使用FFmpeg获取音频信息失败: {e}")
            return None
    
    def standardize_audio(self, input_file: str, target_sample_rate: int = 44100) -> Optional[str]:
        """
        标准化音频文件（转换为指定采样率的WAV格式）
        
        Args:
            input_file: 输入音频文件路径
            target_sample_rate: 目标采样率
            
        Returns:
            str: 标准化后的文件路径，失败返回None
        """
        try:
            if not Path(input_file).exists():
                self.logger.error(f"输入文件不存在: {input_file}")
                return None
            
            # 生成输出文件名
            input_path = Path(input_file)
            output_filename = f"standardized_{input_path.stem}.wav"
            output_path = self.temp_dir / output_filename
            
            # 如果输出文件已存在，先删除
            if output_path.exists():
                output_path.unlink()
            
            # 使用FFmpeg进行转换
            cmd = [
                self.ffmpeg_path,
                "-i", input_file,
                "-ar", str(target_sample_rate),
                "-ac", "2",  # 立体声
                "-y",  # 覆盖输出文件
                str(output_path)
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0 and output_path.exists():
                self.logger.info(f"音频标准化成功: {input_file} -> {output_path}")
                return str(output_path)
            else:
                self.logger.error(f"音频标准化失败: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.error(f"音频标准化异常: {e}")
            return None
    
    def convert_format(self, input_file: str, output_format: str) -> Optional[str]:
        """
        转换音频格式
        
        Args:
            input_file: 输入文件路径
            output_format: 输出格式 (wav, mp3, flac等)
            
        Returns:
            str: 转换后的文件路径，失败返回None
        """
        try:
            if not Path(input_file).exists():
                self.logger.error(f"输入文件不存在: {input_file}")
                return None
            
            # 生成输出文件名
            input_path = Path(input_file)
            output_filename = f"{input_path.stem}.{output_format.lower()}"
            output_path = self.temp_dir / output_filename
            
            # 如果输出文件已存在，先删除
            if output_path.exists():
                output_path.unlink()
            
            # 使用FFmpeg进行转换
            cmd = [self.ffmpeg_path, "-i", input_file, "-y", str(output_path)]
            
            # 根据格式添加特定参数
            if output_format.lower() == 'mp3':
                cmd.insert(-2, "-codec:a")
                cmd.insert(-2, "libmp3lame")
                cmd.insert(-2, "-b:a")
                cmd.insert(-2, "192k")
            elif output_format.lower() == 'flac':
                cmd.insert(-2, "-codec:a")
                cmd.insert(-2, "flac")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0 and output_path.exists():
                self.logger.info(f"格式转换成功: {input_file} -> {output_path}")
                return str(output_path)
            else:
                self.logger.error(f"格式转换失败: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.error(f"格式转换异常: {e}")
            return None
    
    def is_audio_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的音频格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是音频文件返回True
        """
        return Path(file_path).suffix.lower() in self.supported_formats
    
    def cleanup_temp_files(self, pattern: str = "temp_*"):
        """
        清理临时文件
        
        Args:
            pattern: 文件名模式
        """
        try:
            deleted_count = 0
            for file_path in self.temp_dir.glob(pattern):
                if file_path.is_file():
                    file_path.unlink()
                    deleted_count += 1
            
            self.logger.info(f"清理临时文件完成，删除 {deleted_count} 个文件")
            
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
    
    def get_temp_file_path(self, filename: str) -> str:
        """
        获取临时文件路径
        
        Args:
            filename: 文件名
            
        Returns:
            str: 临时文件完整路径
        """
        return str(self.temp_dir / filename)
