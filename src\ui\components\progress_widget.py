#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度显示组件 - 提供任务进度的可视化显示
"""

import logging
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QProgressBar, QPushButton, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QMovie


class ProgressWidget(QWidget):
    """进度显示组件"""
    
    # 信号定义
    cancel_requested = Signal()  # 取消请求信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化进度显示组件
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 进度状态
        self.is_active = False
        self.current_progress = 0
        self.current_message = ""
        
        # 创建界面
        self._create_ui()
        
        # 隐藏组件（初始状态）
        self.hide()
        
        self.logger.debug("进度显示组件初始化完成")
    
    def _create_ui(self):
        """创建用户界面"""
        # 主框架
        self.main_frame = QFrame()
        self.main_frame.setObjectName("ProgressFrame")
        self.main_frame.setStyleSheet("""
            QFrame#ProgressFrame {
                background-color: #21262D;
                border: 1px solid #58A6FF;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.main_frame)
        
        # 框架内布局
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(12)
        
        # 标题和状态
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # 状态图标（可以是动画）
        self.status_icon = QLabel("⏳")
        self.status_icon.setStyleSheet("font-size: 20px;")
        self.status_icon.setFixedSize(30, 30)
        self.status_icon.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.status_icon)
        
        # 标题标签
        self.title_label = QLabel("处理中...")
        self.title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Bold))
        self.title_label.setStyleSheet("color: #E6EDF3;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F85149;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF6B6B;
            }
        """)
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        header_layout.addWidget(self.cancel_button)
        
        frame_layout.addLayout(header_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #30363D;
                border-radius: 4px;
                background-color: #1C2128;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #58A6FF;
                border-radius: 3px;
            }
        """)
        frame_layout.addWidget(self.progress_bar)
        
        # 进度信息
        info_layout = QHBoxLayout()
        info_layout.setSpacing(10)
        
        # 进度消息
        self.message_label = QLabel("准备开始...")
        self.message_label.setStyleSheet("color: #8B949E; font-size: 12px;")
        info_layout.addWidget(self.message_label)
        
        info_layout.addStretch()
        
        # 进度百分比
        self.percentage_label = QLabel("0%")
        self.percentage_label.setStyleSheet("color: #E6EDF3; font-size: 12px; font-weight: bold;")
        self.percentage_label.setFixedWidth(40)
        self.percentage_label.setAlignment(Qt.AlignRight)
        info_layout.addWidget(self.percentage_label)
        
        frame_layout.addLayout(info_layout)
        
        # 详细信息（可选显示）
        self.details_label = QLabel()
        self.details_label.setStyleSheet("color: #8B949E; font-size: 10px;")
        self.details_label.setWordWrap(True)
        self.details_label.hide()  # 默认隐藏
        frame_layout.addWidget(self.details_label)
        
        # 动画定时器（用于状态图标动画）
        self.animation_timer = QTimer()
        self.animation_timer.setInterval(500)  # 500ms间隔
        self.animation_timer.timeout.connect(self._animate_icon)
        self.animation_states = ["⏳", "⌛"]
        self.animation_index = 0
    
    def start_progress(self, title: str = "处理中...", message: str = "准备开始..."):
        """
        开始显示进度
        
        Args:
            title: 进度标题
            message: 进度消息
        """
        self.is_active = True
        self.current_progress = 0
        
        # 更新界面
        self.title_label.setText(title)
        self.message_label.setText(message)
        self.progress_bar.setValue(0)
        self.percentage_label.setText("0%")
        
        # 显示组件
        self.show()
        
        # 开始动画
        self.animation_timer.start()
        
        self.logger.info(f"开始进度显示: {title}")
    
    def update_progress(self, progress: int, message: str = ""):
        """
        更新进度
        
        Args:
            progress: 进度值（0-100）
            message: 进度消息
        """
        if not self.is_active:
            return
        
        # 限制进度值范围
        progress = max(0, min(100, progress))
        self.current_progress = progress
        
        # 更新进度条
        self.progress_bar.setValue(progress)
        self.percentage_label.setText(f"{progress}%")
        
        # 更新消息
        if message:
            self.current_message = message
            self.message_label.setText(message)
        
        self.logger.debug(f"进度更新: {progress}% - {message}")
    
    def finish_progress(self, success: bool = True, message: str = ""):
        """
        完成进度显示
        
        Args:
            success: 是否成功完成
            message: 完成消息
        """
        self.is_active = False
        
        # 停止动画
        self.animation_timer.stop()
        
        if success:
            self.status_icon.setText("✅")
            self.progress_bar.setValue(100)
            self.percentage_label.setText("100%")
            final_message = message or "处理完成"
        else:
            self.status_icon.setText("❌")
            final_message = message or "处理失败"
        
        self.message_label.setText(final_message)
        
        # 延迟隐藏
        QTimer.singleShot(3000, self.hide)  # 3秒后隐藏
        
        self.logger.info(f"进度完成: {'成功' if success else '失败'} - {final_message}")
    
    def cancel_progress(self):
        """取消进度显示"""
        self.is_active = False
        self.animation_timer.stop()
        
        self.status_icon.setText("⏹")
        self.message_label.setText("已取消")
        
        # 延迟隐藏
        QTimer.singleShot(2000, self.hide)  # 2秒后隐藏
        
        self.logger.info("进度已取消")
    
    def set_details(self, details: str):
        """
        设置详细信息
        
        Args:
            details: 详细信息文本
        """
        if details:
            self.details_label.setText(details)
            self.details_label.show()
        else:
            self.details_label.hide()
    
    def _animate_icon(self):
        """动画状态图标"""
        if self.is_active:
            self.status_icon.setText(self.animation_states[self.animation_index])
            self.animation_index = (self.animation_index + 1) % len(self.animation_states)
    
    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        self.cancel_requested.emit()
        self.cancel_progress()
    
    def is_progress_active(self) -> bool:
        """
        检查进度是否处于活动状态
        
        Returns:
            bool: 活动状态返回True
        """
        return self.is_active
    
    def get_current_progress(self) -> int:
        """
        获取当前进度值
        
        Returns:
            int: 当前进度（0-100）
        """
        return self.current_progress
