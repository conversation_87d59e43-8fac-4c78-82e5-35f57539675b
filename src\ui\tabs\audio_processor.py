#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻唱处理选项卡 - 负责音频上传、处理参数配置和翻唱处理
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QSlider, QCheckBox, QFileDialog, QFrame, QSplitter,
    QScrollArea, QWidget, QGroupBox, QGridLayout, QSpinBox,
    QDoubleSpinBox, QLineEdit
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent

from .base_tab import BaseTab
from core.config_manager import ConfigManager
from ui.components.audio_player import AudioPlayerWidget
from ui.components.progress_widget import ProgressWidget


class AudioUploadWidget(QFrame):
    """音频上传组件"""
    
    file_selected = Signal(str)  # 文件选择信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """初始化音频上传组件"""
        super().__init__(parent)
        
        self.setAcceptDrops(True)
        self.selected_file = None
        
        self._create_ui()
    
    def _create_ui(self):
        """创建用户界面"""
        self.setFixedHeight(150)
        self.setStyleSheet("""
            QFrame {
                border: 2px dashed #58A6FF;
                border-radius: 8px;
                background-color: #1C2128;
            }
            QFrame:hover {
                background-color: #21262D;
                border-color: #79C0FF;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(10)
        
        # 上传图标
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; border: none;")
        layout.addWidget(icon_label)
        
        # 提示文本
        self.hint_label = QLabel("点击选择音频文件或拖拽文件到此处")
        self.hint_label.setAlignment(Qt.AlignCenter)
        self.hint_label.setStyleSheet("color: #8B949E; font-size: 14px; border: none;")
        layout.addWidget(self.hint_label)
        
        # 支持格式提示
        format_label = QLabel("支持格式: WAV, MP3, FLAC, M4A")
        format_label.setAlignment(Qt.AlignCenter)
        format_label.setStyleSheet("color: #8B949E; font-size: 12px; border: none;")
        layout.addWidget(format_label)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self._select_file()
        super().mousePressEvent(event)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            if self._is_audio_file(file_path):
                self._set_selected_file(file_path)
            event.acceptProposedAction()
    
    def _select_file(self):
        """选择文件对话框"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.m4a);;所有文件 (*)"
        )
        
        if file_path:
            self._set_selected_file(file_path)
    
    def _set_selected_file(self, file_path: str):
        """设置选中的文件"""
        self.selected_file = file_path
        filename = Path(file_path).name
        self.hint_label.setText(f"已选择: {filename}")
        self.file_selected.emit(file_path)
    
    def _is_audio_file(self, file_path: str) -> bool:
        """检查是否为音频文件"""
        audio_extensions = {'.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg'}
        return Path(file_path).suffix.lower() in audio_extensions
    
    def clear_selection(self):
        """清除选择"""
        self.selected_file = None
        self.hint_label.setText("点击选择音频文件或拖拽文件到此处")


class AudioProcessorTab(BaseTab):
    """翻唱处理选项卡"""
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """初始化翻唱处理选项卡"""
        self.selected_audio_file = None
        self.available_models = []
        self.available_configs = []
        
        super().__init__(config_manager, parent)

        # 加载模型列表
        self._load_models()

        # 加载默认配置
        self._load_default_settings()
    
    def _create_content(self):
        """创建选项卡内容"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(splitter)
        
        # 左侧：音频上传和预览
        self._create_audio_panel(splitter)
        
        # 右侧：处理参数和控制
        self._create_control_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([500, 750])
    
    def _create_audio_panel(self, parent):
        """创建音频面板"""
        audio_frame = self.create_section_frame("音频文件")
        audio_layout = audio_frame.layout()
        
        # 音频上传组件
        self.upload_widget = AudioUploadWidget()
        self.upload_widget.file_selected.connect(self._on_file_selected)
        audio_layout.addWidget(self.upload_widget)
        
        # 音频播放器
        self.audio_player = AudioPlayerWidget()
        self.audio_player.setMinimumHeight(200)
        audio_layout.addWidget(self.audio_player)
        
        # 文件信息
        info_frame = self.create_section_frame("文件信息")
        info_layout = info_frame.layout()
        
        self.file_info_label = QLabel("未选择文件")
        self.file_info_label.setStyleSheet("color: #8B949E; font-size: 12px;")
        info_layout.addWidget(self.file_info_label)
        
        audio_layout.addWidget(info_frame)
        
        parent.addWidget(audio_frame)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(15)
        
        # 模型选择区域
        self._create_model_section(control_layout)

        # 处理参数区域
        self._create_parameters_section(control_layout)

        # 高级参数配置区域
        self._create_advanced_config_section(control_layout)

        # 混响设置区域
        self._create_reverb_section(control_layout)

        # 处理控制区域
        self._create_processing_section(control_layout)
        
        # 进度显示
        self.progress_widget = ProgressWidget()
        control_layout.addWidget(self.progress_widget)
        
        control_layout.addStretch()
        
        scroll_area.setWidget(control_widget)
        parent.addWidget(scroll_area)
    
    def _create_model_section(self, parent_layout):
        """创建模型选择区域"""
        model_frame = self.create_section_frame("模型选择")
        model_layout = model_frame.layout()
        
        # 模型文件选择
        model_row = QHBoxLayout()
        model_row.addWidget(QLabel("模型文件:"))
        
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(200)
        self._update_model_combo()
        model_row.addWidget(self.model_combo)
        
        refresh_model_btn = QPushButton("🔄")
        refresh_model_btn.setFixedSize(30, 30)
        refresh_model_btn.setToolTip("刷新模型列表")
        refresh_model_btn.clicked.connect(self._load_models)
        model_row.addWidget(refresh_model_btn)
        
        model_layout.addLayout(model_row)
        
        # 配置文件选择
        config_row = QHBoxLayout()
        config_row.addWidget(QLabel("配置文件:"))
        
        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(200)
        self._update_config_combo()
        config_row.addWidget(self.config_combo)
        
        refresh_config_btn = QPushButton("🔄")
        refresh_config_btn.setFixedSize(30, 30)
        refresh_config_btn.setToolTip("刷新配置列表")
        refresh_config_btn.clicked.connect(self._load_models)
        config_row.addWidget(refresh_config_btn)
        
        model_layout.addLayout(config_row)
        
        parent_layout.addWidget(model_frame)
    
    def _create_parameters_section(self, parent_layout):
        """创建处理参数区域"""
        params_frame = self.create_section_frame("处理参数")
        params_layout = params_frame.layout()

        # 参数网格
        grid = QGridLayout()

        # 人声音高
        grid.addWidget(QLabel("人声音高:"), 0, 0)
        self.vocal_pitch_slider = QSlider(Qt.Horizontal)
        self.vocal_pitch_slider.setRange(-12, 12)
        self.vocal_pitch_slider.setValue(0)
        grid.addWidget(self.vocal_pitch_slider, 0, 1)
        self.vocal_pitch_label = QLabel("0")
        grid.addWidget(self.vocal_pitch_label, 0, 2)

        # 伴奏音高
        grid.addWidget(QLabel("伴奏音高:"), 1, 0)
        self.instrumental_pitch_slider = QSlider(Qt.Horizontal)
        self.instrumental_pitch_slider.setRange(-12, 12)
        self.instrumental_pitch_slider.setValue(0)
        grid.addWidget(self.instrumental_pitch_slider, 1, 1)
        self.instrumental_pitch_label = QLabel("0")
        grid.addWidget(self.instrumental_pitch_label, 1, 2)

        # 输出格式
        grid.addWidget(QLabel("输出格式:"), 2, 0)
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["WAV", "MP3", "FLAC"])
        grid.addWidget(self.output_format_combo, 2, 1, 1, 2)

        params_layout.addLayout(grid)

        # 连接信号
        self.vocal_pitch_slider.valueChanged.connect(
            lambda v: self.vocal_pitch_label.setText(str(v))
        )
        self.instrumental_pitch_slider.valueChanged.connect(
            lambda v: self.instrumental_pitch_label.setText(str(v))
        )

        parent_layout.addWidget(params_frame)

    def _create_advanced_config_section(self, parent_layout):
        """创建高级参数配置区域"""
        advanced_frame = self.create_section_frame("高级参数配置")
        advanced_layout = advanced_frame.layout()

        # 第一行：声码器和F0提取器
        row1_layout = QHBoxLayout()

        # 声码器
        row1_layout.addWidget(QLabel("声码器:"))
        self.vocoder_combo = QComboBox()
        self.vocoder_combo.addItems(["pc_nsf_hifigan_testing", "nsf_hifigan", "pc_ddsp"])
        self.vocoder_combo.setCurrentText("pc_nsf_hifigan_testing")
        row1_layout.addWidget(self.vocoder_combo)

        row1_layout.addSpacing(20)

        # F0提取器
        row1_layout.addWidget(QLabel("F0提取器:"))
        self.f0_extractor_combo = QComboBox()
        self.f0_extractor_combo.addItems(["rmvpe (默认)", "harvest", "crepe", "mangio-crepe"])
        self.f0_extractor_combo.setCurrentText("rmvpe (默认)")
        row1_layout.addWidget(self.f0_extractor_combo)

        row1_layout.addStretch()
        advanced_layout.addLayout(row1_layout)

        # 第二行：共振峰偏移和采样步数
        row2_layout = QHBoxLayout()

        # 共振峰偏移
        row2_layout.addWidget(QLabel("共振峰偏移:"))
        self.formant_shift_slider = QSlider(Qt.Horizontal)
        self.formant_shift_slider.setRange(-30, 30)
        self.formant_shift_slider.setValue(0)
        self.formant_shift_slider.setFixedWidth(150)
        row2_layout.addWidget(self.formant_shift_slider)
        self.formant_shift_label = QLabel("0")
        self.formant_shift_label.setFixedWidth(30)
        row2_layout.addWidget(self.formant_shift_label)

        row2_layout.addSpacing(20)

        # 采样步数
        row2_layout.addWidget(QLabel("采样步数:"))
        self.sampling_steps_spinbox = QSpinBox()
        self.sampling_steps_spinbox.setRange(1, 1000)
        self.sampling_steps_spinbox.setValue(50)
        self.sampling_steps_spinbox.setFixedWidth(80)
        row2_layout.addWidget(self.sampling_steps_spinbox)

        row2_layout.addStretch()
        advanced_layout.addLayout(row2_layout)

        # 第三行：采样器和设备选择
        row3_layout = QHBoxLayout()

        # 采样器
        row3_layout.addWidget(QLabel("采样器:"))
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "ddim", "dpmsolver", "unipc"])
        self.sampler_combo.setCurrentText("euler")
        row3_layout.addWidget(self.sampler_combo)

        row3_layout.addSpacing(20)

        # 设备选择
        row3_layout.addWidget(QLabel("设备选择:"))
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CPU", "CUDA", "Auto"])
        self.device_combo.setCurrentText("CPU")
        row3_layout.addWidget(self.device_combo)

        row3_layout.addStretch()
        advanced_layout.addLayout(row3_layout)

        # 连接信号
        self.formant_shift_slider.valueChanged.connect(
            lambda v: self.formant_shift_label.setText(str(v))
        )

        parent_layout.addWidget(advanced_frame)
    
    def _create_reverb_section(self, parent_layout):
        """创建混响设置区域"""
        reverb_frame = self.create_section_frame("混响设置")
        reverb_layout = reverb_frame.layout()
        
        # 混响开关
        self.reverb_enabled = QCheckBox("启用混响")
        reverb_layout.addWidget(self.reverb_enabled)
        
        # 混响参数
        reverb_params = QGridLayout()
        
        # 房间大小
        reverb_params.addWidget(QLabel("房间大小:"), 0, 0)
        self.room_size_slider = QSlider(Qt.Horizontal)
        self.room_size_slider.setRange(0, 100)
        self.room_size_slider.setValue(30)
        reverb_params.addWidget(self.room_size_slider, 0, 1)
        self.room_size_label = QLabel("0.3")
        reverb_params.addWidget(self.room_size_label, 0, 2)
        
        # 阻尼
        reverb_params.addWidget(QLabel("阻尼:"), 1, 0)
        self.damping_slider = QSlider(Qt.Horizontal)
        self.damping_slider.setRange(0, 100)
        self.damping_slider.setValue(10)
        reverb_params.addWidget(self.damping_slider, 1, 1)
        self.damping_label = QLabel("0.1")
        reverb_params.addWidget(self.damping_label, 1, 2)
        
        # 湿声电平
        reverb_params.addWidget(QLabel("湿声电平:"), 2, 0)
        self.wet_level_slider = QSlider(Qt.Horizontal)
        self.wet_level_slider.setRange(0, 100)
        self.wet_level_slider.setValue(20)
        reverb_params.addWidget(self.wet_level_slider, 2, 1)
        self.wet_level_label = QLabel("0.2")
        reverb_params.addWidget(self.wet_level_label, 2, 2)
        
        reverb_layout.addLayout(reverb_params)
        
        # 连接信号
        self.room_size_slider.valueChanged.connect(
            lambda v: self.room_size_label.setText(f"{v/100:.1f}")
        )
        self.damping_slider.valueChanged.connect(
            lambda v: self.damping_label.setText(f"{v/100:.1f}")
        )
        self.wet_level_slider.valueChanged.connect(
            lambda v: self.wet_level_label.setText(f"{v/100:.1f}")
        )
        
        parent_layout.addWidget(reverb_frame)
    
    def _create_processing_section(self, parent_layout):
        """创建处理控制区域"""
        processing_frame = self.create_section_frame("处理控制")
        processing_layout = processing_frame.layout()
        
        # 处理按钮
        button_layout = QHBoxLayout()
        
        self.process_button = QPushButton("🚀 开始翻唱")
        self.process_button.setMinimumHeight(50)
        self.process_button.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                background-color: #58A6FF;
            }
            QPushButton:hover {
                background-color: #79C0FF;
            }
            QPushButton:disabled {
                background-color: #30363D;
                color: #8B949E;
            }
        """)
        self.process_button.clicked.connect(self._start_processing)
        self.process_button.setEnabled(False)
        button_layout.addWidget(self.process_button)
        
        self.stop_button = QPushButton("⏹ 停止")
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                background-color: #F85149;
            }
            QPushButton:hover {
                background-color: #FF6B6B;
            }
        """)
        self.stop_button.clicked.connect(self._stop_processing)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        processing_layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #8B949E; font-size: 14px; margin: 10px;")
        processing_layout.addWidget(self.status_label)
        
        parent_layout.addWidget(processing_frame)

    def _load_models(self):
        """加载模型和配置文件列表"""
        try:
            models_dir = Path("models")
            if not models_dir.exists():
                models_dir.mkdir()
                self.logger.warning("models目录不存在，已创建")

            # 扫描模型文件
            self.available_models = []
            self.available_configs = []

            for file_path in models_dir.glob("*.pt"):
                self.available_models.append(file_path.name)

            for file_path in models_dir.glob("*.yaml"):
                self.available_configs.append(file_path.name)

            # 更新下拉框
            self._update_model_combo()
            self._update_config_combo()

            self.logger.info(f"加载模型: {len(self.available_models)}个, 配置: {len(self.available_configs)}个")

        except Exception as e:
            self.logger.error(f"加载模型列表失败: {e}")

    def _update_model_combo(self):
        """更新模型下拉框"""
        self.model_combo.clear()
        if self.available_models:
            self.model_combo.addItems(self.available_models)
        else:
            self.model_combo.addItem("未找到模型文件")

    def _update_config_combo(self):
        """更新配置下拉框"""
        self.config_combo.clear()
        if self.available_configs:
            self.config_combo.addItems(self.available_configs)
        else:
            self.config_combo.addItem("未找到配置文件")

    def _on_file_selected(self, file_path: str):
        """文件选择处理"""
        try:
            self.selected_audio_file = file_path

            # 加载到音频播放器
            if self.audio_player.load_audio(file_path):
                # 更新文件信息
                file_info = self._get_file_info(file_path)
                self.file_info_label.setText(file_info)

                # 启用处理按钮
                self.process_button.setEnabled(True)
                self.process_button.setToolTip("点击开始翻唱处理")

                self.emit_status_message(f"已选择音频文件: {Path(file_path).name}")
            else:
                self.show_error_message("文件加载失败", "无法加载选择的音频文件")

        except Exception as e:
            self.logger.error(f"文件选择处理失败: {e}")
            self.show_error_message("文件选择失败", str(e))

    def _get_file_info(self, file_path: str) -> str:
        """获取文件信息"""
        try:
            file_path_obj = Path(file_path)
            file_size = file_path_obj.stat().st_size

            # 格式化文件大小
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"

            return f"文件名: {file_path_obj.name}\n大小: {size_str}\n格式: {file_path_obj.suffix.upper()}"

        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return "文件信息获取失败"

    def _start_processing(self):
        """开始处理"""
        if not self.selected_audio_file:
            self.show_warning_message("处理失败", "请先选择音频文件")
            return

        if not self.available_models:
            self.show_warning_message("处理失败", "未找到可用的模型文件")
            return

        try:
            # 获取处理配置
            config = self._get_processing_config()

            # 更新界面状态
            self.process_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("状态: 处理中...")

            # 显示进度
            self.progress_widget.start_progress("音频处理中", "正在准备处理...")

            # TODO: 启动处理工作线程
            # 这里需要实现实际的处理逻辑
            self._simulate_processing()

            self.emit_status_message("开始音频处理")

        except Exception as e:
            self.logger.error(f"开始处理失败: {e}")
            self.show_error_message("处理失败", str(e))
            self._reset_processing_state()

    def _stop_processing(self):
        """停止处理"""
        try:
            # TODO: 停止处理工作线程

            # 更新界面状态
            self._reset_processing_state()
            self.progress_widget.cancel_progress()

            self.emit_status_message("处理已停止")

        except Exception as e:
            self.logger.error(f"停止处理失败: {e}")

    def _get_processing_config(self) -> Dict[str, Any]:
        """获取处理配置"""
        return {
            'model_file': self.model_combo.currentText(),
            'config_file': self.config_combo.currentText(),
            'vocal_pitch': self.vocal_pitch_slider.value(),
            'instrumental_pitch': self.instrumental_pitch_slider.value(),
            'output_format': self.output_format_combo.currentText().lower(),
            'reverb_enabled': self.reverb_enabled.isChecked(),
            'room_size': self.room_size_slider.value() / 100.0,
            'damping': self.damping_slider.value() / 100.0,
            'wet_level': self.wet_level_slider.value() / 100.0,
            # 高级参数
            'vocoder': self.vocoder_combo.currentText(),
            'f0_extractor': self.f0_extractor_combo.currentText(),
            'formant_shift': self.formant_shift_slider.value(),
            'sampling_steps': self.sampling_steps_spinbox.value(),
            'sampler': self.sampler_combo.currentText(),
            'device': self.device_combo.currentText(),
        }

    def _reset_processing_state(self):
        """重置处理状态"""
        self.process_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("状态: 就绪")

    def _simulate_processing(self):
        """模拟处理过程（用于测试）"""
        from PySide6.QtCore import QTimer

        self.simulation_progress = 0
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self._update_simulation)
        self.simulation_timer.start(100)  # 每100ms更新一次

    def _update_simulation(self):
        """更新模拟进度"""
        self.simulation_progress += 2

        if self.simulation_progress <= 100:
            messages = [
                "正在分析音频文件...",
                "正在进行音频分离...",
                "正在加载AI模型...",
                "正在进行音色转换...",
                "正在应用音效处理...",
                "正在生成最终音频...",
                "处理完成"
            ]

            message_index = min(self.simulation_progress // 15, len(messages) - 1)
            self.progress_widget.update_progress(self.simulation_progress, messages[message_index])
        else:
            self.simulation_timer.stop()
            self.progress_widget.finish_progress(True, "处理完成！")
            self._reset_processing_state()
            self.emit_status_message("音频处理完成")

    def _load_default_settings(self):
        """加载默认设置"""
        try:
            processing_config = self.config_manager.get_processing_config()

            # 设置默认值
            self.vocal_pitch_slider.setValue(processing_config.vocal_pitch)
            self.instrumental_pitch_slider.setValue(processing_config.instrumental_pitch)
            self.reverb_enabled.setChecked(processing_config.reverb_enabled)
            self.room_size_slider.setValue(int(processing_config.reverb_room_size * 100))
            self.damping_slider.setValue(int(processing_config.reverb_damping * 100))
            self.wet_level_slider.setValue(int(processing_config.reverb_wet_level * 100))

            # 设置高级参数默认值
            if hasattr(self, 'vocoder_combo'):
                self.vocoder_combo.setCurrentText(processing_config.vocoder)
            if hasattr(self, 'f0_extractor_combo'):
                self.f0_extractor_combo.setCurrentText(processing_config.f0_extractor)
            if hasattr(self, 'formant_shift_slider'):
                self.formant_shift_slider.setValue(processing_config.formant_shift)
            if hasattr(self, 'sampling_steps_spinbox'):
                self.sampling_steps_spinbox.setValue(processing_config.sampling_steps)
            if hasattr(self, 'sampler_combo'):
                self.sampler_combo.setCurrentText(processing_config.sampler)
            if hasattr(self, 'device_combo'):
                self.device_combo.setCurrentText(processing_config.device)

            self.logger.info("默认设置加载完成")

        except Exception as e:
            self.logger.error(f"加载默认设置失败: {e}")

    def open_file_dialog(self):
        """打开文件选择对话框（供外部调用）"""
        self.upload_widget._select_file()
