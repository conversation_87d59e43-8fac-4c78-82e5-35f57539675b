#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具函数 - 提供文件操作相关的实用函数
"""

import os
import shutil
import hashlib
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """
        确保目录存在，不存在则创建
        
        Args:
            directory: 目录路径
            
        Returns:
            bool: 成功返回True
        """
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"创建目录失败 {directory}: {e}")
            return False
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """
        生成安全的文件名（移除非法字符）
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 安全的文件名
        """
        # 定义非法字符
        illegal_chars = '<>:"/\\|?*'
        
        # 替换非法字符
        safe_name = filename
        for char in illegal_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 移除前后空格和点
        safe_name = safe_name.strip(' .')
        
        # 确保文件名不为空
        if not safe_name:
            safe_name = "unnamed"
        
        # 限制长度
        if len(safe_name) > 200:
            name_part, ext_part = os.path.splitext(safe_name)
            safe_name = name_part[:200-len(ext_part)] + ext_part
        
        return safe_name
    
    @staticmethod
    def get_unique_filename(file_path: str) -> str:
        """
        获取唯一的文件名（如果文件已存在，添加数字后缀）
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 唯一的文件路径
        """
        path_obj = Path(file_path)
        
        if not path_obj.exists():
            return file_path
        
        # 分离文件名和扩展名
        stem = path_obj.stem
        suffix = path_obj.suffix
        parent = path_obj.parent
        
        # 查找可用的文件名
        counter = 1
        while True:
            new_name = f"{stem}_{counter}{suffix}"
            new_path = parent / new_name
            
            if not new_path.exists():
                return str(new_path)
            
            counter += 1
            
            # 防止无限循环
            if counter > 9999:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_name = f"{stem}_{timestamp}{suffix}"
                return str(parent / new_name)
    
    @staticmethod
    def copy_file_safe(src: str, dst: str, overwrite: bool = False) -> bool:
        """
        安全地复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            # 检查源文件是否存在
            if not src_path.exists():
                logging.error(f"源文件不存在: {src}")
                return False
            
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查是否需要覆盖
            if dst_path.exists() and not overwrite:
                dst = FileUtils.get_unique_filename(dst)
                dst_path = Path(dst)
            
            # 复制文件
            shutil.copy2(src, dst_path)
            logging.info(f"文件复制成功: {src} -> {dst_path}")
            return True
            
        except Exception as e:
            logging.error(f"文件复制失败: {e}")
            return False
    
    @staticmethod
    def move_file_safe(src: str, dst: str, overwrite: bool = False) -> bool:
        """
        安全地移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            # 检查源文件是否存在
            if not src_path.exists():
                logging.error(f"源文件不存在: {src}")
                return False
            
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查是否需要覆盖
            if dst_path.exists() and not overwrite:
                dst = FileUtils.get_unique_filename(dst)
                dst_path = Path(dst)
            
            # 移动文件
            shutil.move(src, dst_path)
            logging.info(f"文件移动成功: {src} -> {dst_path}")
            return True
            
        except Exception as e:
            logging.error(f"文件移动失败: {e}")
            return False
    
    @staticmethod
    def delete_file_safe(file_path: str) -> bool:
        """
        安全地删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 成功返回True
        """
        try:
            path_obj = Path(file_path)
            
            if path_obj.exists():
                if path_obj.is_file():
                    path_obj.unlink()
                    logging.info(f"文件删除成功: {file_path}")
                else:
                    logging.warning(f"路径不是文件: {file_path}")
                    return False
            else:
                logging.warning(f"文件不存在: {file_path}")
            
            return True
            
        except Exception as e:
            logging.error(f"文件删除失败: {e}")
            return False
    
    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            str: 哈希值，失败返回None
        """
        try:
            if algorithm.lower() == "md5":
                hash_obj = hashlib.md5()
            elif algorithm.lower() == "sha1":
                hash_obj = hashlib.sha1()
            elif algorithm.lower() == "sha256":
                hash_obj = hashlib.sha256()
            else:
                logging.error(f"不支持的哈希算法: {algorithm}")
                return None
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            logging.error(f"计算文件哈希失败: {e}")
            return None
    
    @staticmethod
    def cleanup_directory(
        directory: str, 
        pattern: str = "*",
        max_age_days: Optional[int] = None,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        清理目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件名模式
            max_age_days: 最大文件年龄（天），None表示不限制
            dry_run: 是否为试运行（不实际删除）
            
        Returns:
            dict: 清理结果统计
        """
        try:
            dir_path = Path(directory)
            if not dir_path.exists():
                return {"error": "目录不存在"}
            
            # 统计信息
            stats = {
                "total_files": 0,
                "deleted_files": 0,
                "deleted_size": 0,
                "errors": []
            }
            
            # 当前时间
            now = datetime.now()
            
            # 遍历文件
            for file_path in dir_path.glob(pattern):
                if not file_path.is_file():
                    continue
                
                stats["total_files"] += 1
                
                # 检查文件年龄
                if max_age_days is not None:
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    age_days = (now - file_time).days
                    
                    if age_days < max_age_days:
                        continue
                
                # 获取文件大小
                file_size = file_path.stat().st_size
                
                # 删除文件
                if not dry_run:
                    try:
                        file_path.unlink()
                        stats["deleted_files"] += 1
                        stats["deleted_size"] += file_size
                    except Exception as e:
                        stats["errors"].append(f"删除失败 {file_path}: {e}")
                else:
                    stats["deleted_files"] += 1
                    stats["deleted_size"] += file_size
            
            return stats
            
        except Exception as e:
            logging.error(f"清理目录失败: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """
        获取目录总大小
        
        Args:
            directory: 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        try:
            total_size = 0
            dir_path = Path(directory)
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            
            return total_size
            
        except Exception as e:
            logging.error(f"获取目录大小失败: {e}")
            return 0
    
    @staticmethod
    def find_files(
        directory: str,
        pattern: str = "*",
        recursive: bool = True,
        include_dirs: bool = False
    ) -> List[str]:
        """
        查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件名模式
            recursive: 是否递归搜索
            include_dirs: 是否包含目录
            
        Returns:
            List[str]: 找到的文件路径列表
        """
        try:
            dir_path = Path(directory)
            if not dir_path.exists():
                return []
            
            files = []
            
            if recursive:
                search_pattern = f"**/{pattern}"
                glob_func = dir_path.rglob
            else:
                search_pattern = pattern
                glob_func = dir_path.glob
            
            for path in glob_func(search_pattern):
                if include_dirs or path.is_file():
                    files.append(str(path))
            
            return sorted(files)
            
        except Exception as e:
            logging.error(f"查找文件失败: {e}")
            return []
    
    @staticmethod
    def create_backup(file_path: str, backup_dir: Optional[str] = None) -> Optional[str]:
        """
        创建文件备份
        
        Args:
            file_path: 原文件路径
            backup_dir: 备份目录，None表示在原文件目录
            
        Returns:
            str: 备份文件路径，失败返回None
        """
        try:
            src_path = Path(file_path)
            if not src_path.exists():
                logging.error(f"源文件不存在: {file_path}")
                return None
            
            # 确定备份目录
            if backup_dir:
                backup_path = Path(backup_dir)
                backup_path.mkdir(parents=True, exist_ok=True)
            else:
                backup_path = src_path.parent
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{src_path.stem}_backup_{timestamp}{src_path.suffix}"
            backup_file_path = backup_path / backup_filename
            
            # 复制文件
            shutil.copy2(src_path, backup_file_path)
            logging.info(f"备份创建成功: {file_path} -> {backup_file_path}")
            
            return str(backup_file_path)
            
        except Exception as e:
            logging.error(f"创建备份失败: {e}")
            return None
