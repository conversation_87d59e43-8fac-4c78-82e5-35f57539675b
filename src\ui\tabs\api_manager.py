#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理选项卡 - 负责本地/云端API服务的配置和管理
"""

import logging
import subprocess
import threading
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QSpinBox, QCheckBox, QGroupBox, QTextEdit, QFrame,
    QGridLayout, QComboBox, QProgressBar, QWidget
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from core.config_manager import ConfigManager


class APIServerThread(QThread):
    """API服务器线程"""
    
    server_started = Signal(bool, str)  # 服务器启动信号
    server_stopped = Signal()  # 服务器停止信号
    log_message = Signal(str)  # 日志消息信号
    
    def __init__(self, host: str, port: int):
        """
        初始化API服务器线程
        
        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        super().__init__()
        
        self.host = host
        self.port = port
        self.process = None
        self.should_stop = False
        
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """运行API服务器"""
        try:
            # 检查API服务器脚本是否存在
            api_script = Path("api_server.py")
            if not api_script.exists():
                self.server_started.emit(False, "API服务器脚本不存在")
                return
            
            # 启动API服务器
            cmd = [
                "python", str(api_script),
                "--host", self.host,
                "--port", str(self.port)
            ]
            
            self.log_message.emit(f"启动命令: {' '.join(cmd)}")
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.server_started.emit(True, f"API服务器已启动在 {self.host}:{self.port}")
            
            # 读取输出
            for line in iter(self.process.stdout.readline, ''):
                if self.should_stop:
                    break
                self.log_message.emit(line.strip())
            
            self.process.wait()
            
        except Exception as e:
            self.logger.error(f"API服务器启动失败: {e}")
            self.server_started.emit(False, f"启动失败: {str(e)}")
        finally:
            self.server_stopped.emit()
    
    def stop(self):
        """停止API服务器"""
        self.should_stop = True
        if self.process:
            self.process.terminate()
            self.process.wait()
        self.quit()
        self.wait()


class APIManagerTab(BaseTab):
    """API管理选项卡"""
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """初始化API管理选项卡"""
        self.api_server_thread = None
        self.is_server_running = False
        
        super().__init__(config_manager, parent)
        
        # 加载配置
        self._load_api_config()
    
    def _create_content(self):
        """创建选项卡内容"""
        # 本地API配置
        self._create_local_api_section()
        
        # 远程API配置
        self._create_remote_api_section()
        
        # API服务状态
        self._create_status_section()
        
        # 日志显示
        self._create_log_section()
        
        # 添加弹性空间
        self.main_layout.addStretch()
    
    def _create_local_api_section(self):
        """创建本地API配置区域"""
        local_frame = self.create_section_frame("本地API服务")
        local_layout = local_frame.layout()
        
        # 启用本地API
        self.local_enabled_checkbox = QCheckBox("启用本地API服务")
        self.local_enabled_checkbox.setChecked(True)
        self.local_enabled_checkbox.stateChanged.connect(self._on_local_enabled_changed)
        local_layout.addWidget(self.local_enabled_checkbox)
        
        # 配置网格
        config_grid = QGridLayout()
        
        # 主机地址
        config_grid.addWidget(QLabel("主机地址:"), 0, 0)
        self.local_host_edit = QLineEdit("127.0.0.1")
        config_grid.addWidget(self.local_host_edit, 0, 1)
        
        # 端口
        config_grid.addWidget(QLabel("端口:"), 0, 2)
        self.local_port_spinbox = QSpinBox()
        self.local_port_spinbox.setRange(1000, 65535)
        self.local_port_spinbox.setValue(8000)
        config_grid.addWidget(self.local_port_spinbox, 0, 3)
        
        local_layout.addLayout(config_grid)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_server_button = QPushButton("🚀 启动服务")
        self.start_server_button.clicked.connect(self._start_local_server)
        button_layout.addWidget(self.start_server_button)
        
        self.stop_server_button = QPushButton("⏹ 停止服务")
        self.stop_server_button.clicked.connect(self._stop_local_server)
        self.stop_server_button.setEnabled(False)
        button_layout.addWidget(self.stop_server_button)
        
        self.test_local_button = QPushButton("🔍 测试连接")
        self.test_local_button.clicked.connect(self._test_local_connection)
        button_layout.addWidget(self.test_local_button)
        
        button_layout.addStretch()
        local_layout.addLayout(button_layout)
        
        self.main_layout.addWidget(local_frame)
    
    def _create_remote_api_section(self):
        """创建远程API配置区域"""
        remote_frame = self.create_section_frame("远程API服务")
        remote_layout = remote_frame.layout()
        
        # 启用远程API
        self.remote_enabled_checkbox = QCheckBox("启用远程API服务")
        self.remote_enabled_checkbox.stateChanged.connect(self._on_remote_enabled_changed)
        remote_layout.addWidget(self.remote_enabled_checkbox)
        
        # 配置网格
        config_grid = QGridLayout()
        
        # 远程URL
        config_grid.addWidget(QLabel("服务器URL:"), 0, 0)
        self.remote_url_edit = QLineEdit()
        self.remote_url_edit.setPlaceholderText("https://api.example.com")
        config_grid.addWidget(self.remote_url_edit, 0, 1, 1, 2)
        
        # 超时设置
        config_grid.addWidget(QLabel("超时时间(秒):"), 1, 0)
        self.timeout_spinbox = QSpinBox()
        self.timeout_spinbox.setRange(5, 300)
        self.timeout_spinbox.setValue(30)
        config_grid.addWidget(self.timeout_spinbox, 1, 1)
        
        # 自动回退
        self.auto_fallback_checkbox = QCheckBox("自动回退到本地")
        self.auto_fallback_checkbox.setChecked(True)
        config_grid.addWidget(self.auto_fallback_checkbox, 1, 2)
        
        remote_layout.addLayout(config_grid)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.test_remote_button = QPushButton("🔍 测试连接")
        self.test_remote_button.clicked.connect(self._test_remote_connection)
        button_layout.addWidget(self.test_remote_button)
        
        self.save_config_button = QPushButton("💾 保存配置")
        self.save_config_button.clicked.connect(self._save_api_config)
        button_layout.addWidget(self.save_config_button)
        
        button_layout.addStretch()
        remote_layout.addLayout(button_layout)
        
        self.main_layout.addWidget(remote_frame)
    
    def _create_status_section(self):
        """创建API服务状态区域"""
        status_frame = self.create_section_frame("服务状态")
        status_layout = status_frame.layout()
        
        # 状态显示网格
        status_grid = QGridLayout()
        
        # 本地服务状态
        status_grid.addWidget(QLabel("本地服务:"), 0, 0)
        self.local_status_label = QLabel("未启动")
        self.local_status_label.setStyleSheet("color: #F85149; font-weight: bold;")
        status_grid.addWidget(self.local_status_label, 0, 1)
        
        # 远程服务状态
        status_grid.addWidget(QLabel("远程服务:"), 1, 0)
        self.remote_status_label = QLabel("未配置")
        self.remote_status_label.setStyleSheet("color: #8B949E; font-weight: bold;")
        status_grid.addWidget(self.remote_status_label, 1, 1)
        
        # 当前使用的服务
        status_grid.addWidget(QLabel("当前服务:"), 2, 0)
        self.current_service_label = QLabel("无")
        self.current_service_label.setStyleSheet("color: #8B949E; font-weight: bold;")
        status_grid.addWidget(self.current_service_label, 2, 1)
        
        status_layout.addLayout(status_grid)
        
        self.main_layout.addWidget(status_frame)
    
    def _create_log_section(self):
        """创建日志显示区域"""
        log_frame = self.create_section_frame("服务日志")
        log_layout = log_frame.layout()
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #0D1117;
                color: #E6EDF3;
                border: 1px solid #30363D;
                border-radius: 6px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        
        self.clear_log_button = QPushButton("🗑 清除日志")
        self.clear_log_button.clicked.connect(self._clear_log)
        log_button_layout.addWidget(self.clear_log_button)
        
        log_button_layout.addStretch()
        log_layout.addLayout(log_button_layout)
        
        self.main_layout.addWidget(log_frame)
    
    def _load_api_config(self):
        """加载API配置"""
        api_config = self.config_manager.get_api_config()
        
        # 加载本地配置
        self.local_enabled_checkbox.setChecked(api_config.local_enabled)
        self.local_host_edit.setText(api_config.local_host)
        self.local_port_spinbox.setValue(api_config.local_port)
        
        # 加载远程配置
        self.remote_enabled_checkbox.setChecked(api_config.remote_enabled)
        self.remote_url_edit.setText(api_config.remote_url)
        self.timeout_spinbox.setValue(api_config.timeout)
        self.auto_fallback_checkbox.setChecked(api_config.auto_fallback)
    
    def _save_api_config(self):
        """保存API配置"""
        try:
            # 更新配置
            success = self.config_manager.update_api_config(
                local_enabled=self.local_enabled_checkbox.isChecked(),
                local_host=self.local_host_edit.text(),
                local_port=self.local_port_spinbox.value(),
                remote_enabled=self.remote_enabled_checkbox.isChecked(),
                remote_url=self.remote_url_edit.text(),
                timeout=self.timeout_spinbox.value(),
                auto_fallback=self.auto_fallback_checkbox.isChecked()
            )
            
            if success:
                self.show_info_message("保存成功", "API配置已保存")
                self.emit_status_message("API配置已保存")
            else:
                self.show_error_message("保存失败", "无法保存API配置")
                
        except Exception as e:
            self.logger.error(f"保存API配置失败: {e}")
            self.show_error_message("保存失败", str(e))
    
    def _start_local_server(self):
        """启动本地API服务器"""
        try:
            if self.is_server_running:
                self.show_warning_message("服务器状态", "服务器已在运行中")
                return
            
            host = self.local_host_edit.text()
            port = self.local_port_spinbox.value()
            
            # 创建并启动服务器线程
            self.api_server_thread = APIServerThread(host, port)
            self.api_server_thread.server_started.connect(self._on_server_started)
            self.api_server_thread.server_stopped.connect(self._on_server_stopped)
            self.api_server_thread.log_message.connect(self._add_log_message)
            
            self.api_server_thread.start()
            
            # 更新界面状态
            self.start_server_button.setEnabled(False)
            self.stop_server_button.setEnabled(True)
            self.local_status_label.setText("启动中...")
            self.local_status_label.setStyleSheet("color: #FFA657; font-weight: bold;")
            
            self._add_log_message("正在启动本地API服务器...")
            
        except Exception as e:
            self.logger.error(f"启动本地服务器失败: {e}")
            self.show_error_message("启动失败", str(e))
    
    def _stop_local_server(self):
        """停止本地API服务器"""
        try:
            if self.api_server_thread:
                self.api_server_thread.stop()
            
            self._add_log_message("正在停止本地API服务器...")
            
        except Exception as e:
            self.logger.error(f"停止本地服务器失败: {e}")
            self.show_error_message("停止失败", str(e))
    
    def _test_local_connection(self):
        """测试本地连接"""
        # TODO: 实现本地连接测试
        self.show_info_message("连接测试", "本地连接测试功能待实现")
    
    def _test_remote_connection(self):
        """测试远程连接"""
        # TODO: 实现远程连接测试
        self.show_info_message("连接测试", "远程连接测试功能待实现")
    
    def _on_local_enabled_changed(self, state):
        """本地API启用状态变化"""
        enabled = state == Qt.Checked
        self.local_host_edit.setEnabled(enabled)
        self.local_port_spinbox.setEnabled(enabled)
        self.start_server_button.setEnabled(enabled and not self.is_server_running)
    
    def _on_remote_enabled_changed(self, state):
        """远程API启用状态变化"""
        enabled = state == Qt.Checked
        self.remote_url_edit.setEnabled(enabled)
        self.timeout_spinbox.setEnabled(enabled)
        self.auto_fallback_checkbox.setEnabled(enabled)
        self.test_remote_button.setEnabled(enabled)
    
    def _on_server_started(self, success: bool, message: str):
        """服务器启动完成处理"""
        if success:
            self.is_server_running = True
            self.local_status_label.setText("运行中")
            self.local_status_label.setStyleSheet("color: #56D364; font-weight: bold;")
            self.current_service_label.setText("本地服务")
            self.current_service_label.setStyleSheet("color: #56D364; font-weight: bold;")
            self.emit_status_message("本地API服务器启动成功")
        else:
            self.start_server_button.setEnabled(True)
            self.stop_server_button.setEnabled(False)
            self.local_status_label.setText("启动失败")
            self.local_status_label.setStyleSheet("color: #F85149; font-weight: bold;")
            self.show_error_message("启动失败", message)
        
        self._add_log_message(message)
    
    def _on_server_stopped(self):
        """服务器停止处理"""
        self.is_server_running = False
        self.start_server_button.setEnabled(True)
        self.stop_server_button.setEnabled(False)
        self.local_status_label.setText("已停止")
        self.local_status_label.setStyleSheet("color: #F85149; font-weight: bold;")
        self.current_service_label.setText("无")
        self.current_service_label.setStyleSheet("color: #8B949E; font-weight: bold;")
        
        self._add_log_message("本地API服务器已停止")
        self.emit_status_message("本地API服务器已停止")
    
    def _add_log_message(self, message: str):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
    
    def _clear_log(self):
        """清除日志"""
        self.log_text.clear()
        self._add_log_message("日志已清除")
