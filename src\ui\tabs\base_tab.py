#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选项卡基类 - 所有选项卡的基础类
"""

import logging
from typing import Optional

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.config_manager import ConfigManager


class BaseTab(QWidget):
    """选项卡基类"""
    
    # 信号定义
    status_message = Signal(str)  # 状态消息信号
    
    def __init__(self, config_manager: ConfigManager, parent: Optional[QWidget] = None):
        """
        初始化基础选项卡
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_manager = config_manager
        
        # 设置基础样式
        self._setup_base_style()
        
        # 创建基础布局
        self._create_base_layout()
        
        # 子类需要实现的方法
        self._create_content()
        
        self.logger.debug(f"{self.__class__.__name__} 初始化完成")
    
    def _setup_base_style(self):
        """设置基础样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #21262D;
                color: #E6EDF3;
            }
            
            QFrame {
                border: 1px solid #30363D;
                border-radius: 8px;
                background-color: #1C2128;
                padding: 10px;
            }
            
            QLabel {
                color: #E6EDF3;
                background-color: transparent;
                border: none;
            }
            
            QPushButton {
                background-color: #58A6FF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #79C0FF;
            }
            
            QPushButton:pressed {
                background-color: #388BFD;
            }
            
            QPushButton:disabled {
                background-color: #30363D;
                color: #8B949E;
            }
        """)
    
    def _create_base_layout(self):
        """创建基础布局"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(15)
    
    def _create_content(self):
        """创建选项卡内容 - 子类需要重写此方法"""
        raise NotImplementedError("子类必须实现 _create_content 方法")
    
    def create_section_frame(self, title: str = "") -> QFrame:
        """
        创建区块框架
        
        Args:
            title: 区块标题
            
        Returns:
            QFrame: 创建的框架
        """
        frame = QFrame()
        frame.setObjectName("SectionFrame")
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        if title:
            title_label = QLabel(title)
            title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Bold))
            title_label.setStyleSheet("color: #E6EDF3; margin-bottom: 5px;")
            layout.addWidget(title_label)
        
        return frame
    
    def create_horizontal_layout(self) -> QHBoxLayout:
        """创建水平布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        return layout
    
    def create_vertical_layout(self) -> QVBoxLayout:
        """创建垂直布局"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        return layout
    
    def emit_status_message(self, message: str):
        """发送状态消息"""
        self.status_message.emit(message)
        self.logger.info(f"状态消息: {message}")
    
    def show_error_message(self, title: str, message: str):
        """显示错误消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(self, title, message)
        self.logger.error(f"错误: {title} - {message}")
    
    def show_info_message(self, title: str, message: str):
        """显示信息消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, title, message)
        self.logger.info(f"信息: {title} - {message}")
    
    def show_warning_message(self, title: str, message: str):
        """显示警告消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, title, message)
        self.logger.warning(f"警告: {title} - {message}")
