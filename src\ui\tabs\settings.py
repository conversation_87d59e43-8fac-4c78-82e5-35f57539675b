#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置选项卡 - 负责应用程序配置和偏好设置
"""

import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QSpinBox, QCheckBox, QLineEdit, QFileDialog, QGroupBox,
    QSlider, QFrame, QGridLayout, QTextEdit, QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from core.config_manager import ConfigManager


class SettingsTab(BaseTab):
    """设置选项卡"""
    
    # 信号定义
    settings_changed = Signal()  # 设置变更信号
    
    def __init__(self, config_manager: Config<PERSON><PERSON><PERSON>, parent: Optional[QWidget] = None):
        """初始化设置选项卡"""
        super().__init__(config_manager, parent)
        
        # 加载当前设置
        self._load_settings()
    
    def _create_content(self):
        """创建选项卡内容"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(10, 10, 10, 10)
        settings_layout.setSpacing(15)
        
        # 界面设置
        self._create_ui_settings_section(settings_layout)
        
        # 音频设置
        self._create_audio_settings_section(settings_layout)
        
        # 处理设置
        self._create_processing_settings_section(settings_layout)
        
        # 高级设置
        self._create_advanced_settings_section(settings_layout)
        
        # 操作按钮
        self._create_action_buttons_section(settings_layout)
        
        settings_layout.addStretch()
        
        scroll_area.setWidget(settings_widget)
        self.main_layout.addWidget(scroll_area)
    
    def _create_ui_settings_section(self, parent_layout):
        """创建界面设置区域"""
        ui_frame = self.create_section_frame("界面设置")
        ui_layout = ui_frame.layout()
        
        # 设置网格
        grid = QGridLayout()
        
        # 主题选择
        grid.addWidget(QLabel("主题:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题", "自动"])
        grid.addWidget(self.theme_combo, 0, 1)
        
        # 语言选择
        grid.addWidget(QLabel("语言:"), 1, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        grid.addWidget(self.language_combo, 1, 1)
        
        # 窗口大小
        grid.addWidget(QLabel("默认窗口宽度:"), 2, 0)
        self.window_width_spinbox = QSpinBox()
        self.window_width_spinbox.setRange(800, 2000)
        self.window_width_spinbox.setSuffix(" px")
        grid.addWidget(self.window_width_spinbox, 2, 1)
        
        grid.addWidget(QLabel("默认窗口高度:"), 3, 0)
        self.window_height_spinbox = QSpinBox()
        self.window_height_spinbox.setRange(600, 1500)
        self.window_height_spinbox.setSuffix(" px")
        grid.addWidget(self.window_height_spinbox, 3, 1)
        
        # 记住窗口状态
        self.remember_window_checkbox = QCheckBox("记住窗口大小和位置")
        grid.addWidget(self.remember_window_checkbox, 4, 0, 1, 2)
        
        ui_layout.addLayout(grid)
        parent_layout.addWidget(ui_frame)
    
    def _create_audio_settings_section(self, parent_layout):
        """创建音频设置区域"""
        audio_frame = self.create_section_frame("音频设置")
        audio_layout = audio_frame.layout()
        
        # 设置网格
        grid = QGridLayout()
        
        # 默认采样率
        grid.addWidget(QLabel("默认采样率:"), 0, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100 Hz", "48000 Hz", "96000 Hz"])
        grid.addWidget(self.sample_rate_combo, 0, 1)
        
        # 默认输出格式
        grid.addWidget(QLabel("默认输出格式:"), 1, 0)
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["WAV", "MP3", "FLAC"])
        grid.addWidget(self.output_format_combo, 1, 1)
        
        # 最大文件大小
        grid.addWidget(QLabel("最大文件大小:"), 2, 0)
        self.max_file_size_spinbox = QSpinBox()
        self.max_file_size_spinbox.setRange(10, 2000)
        self.max_file_size_spinbox.setSuffix(" MB")
        grid.addWidget(self.max_file_size_spinbox, 2, 1)
        
        # 临时文件目录
        grid.addWidget(QLabel("临时文件目录:"), 3, 0)
        temp_layout = QHBoxLayout()
        self.temp_dir_edit = QLineEdit()
        temp_layout.addWidget(self.temp_dir_edit)
        
        self.browse_temp_button = QPushButton("浏览...")
        self.browse_temp_button.clicked.connect(self._browse_temp_directory)
        temp_layout.addWidget(self.browse_temp_button)
        
        grid.addLayout(temp_layout, 3, 1)
        
        audio_layout.addLayout(grid)
        parent_layout.addWidget(audio_frame)
    
    def _create_processing_settings_section(self, parent_layout):
        """创建处理设置区域"""
        processing_frame = self.create_section_frame("处理设置")
        processing_layout = processing_frame.layout()
        
        # 设置网格
        grid = QGridLayout()
        
        # 默认模型
        grid.addWidget(QLabel("默认模型:"), 0, 0)
        self.default_model_combo = QComboBox()
        self._update_model_list()
        grid.addWidget(self.default_model_combo, 0, 1)
        
        # 默认人声音高
        grid.addWidget(QLabel("默认人声音高:"), 1, 0)
        self.default_vocal_pitch_spinbox = QSpinBox()
        self.default_vocal_pitch_spinbox.setRange(-12, 12)
        self.default_vocal_pitch_spinbox.setSuffix(" 半音")
        grid.addWidget(self.default_vocal_pitch_spinbox, 1, 1)
        
        # 默认伴奏音高
        grid.addWidget(QLabel("默认伴奏音高:"), 2, 0)
        self.default_instrumental_pitch_spinbox = QSpinBox()
        self.default_instrumental_pitch_spinbox.setRange(-12, 12)
        self.default_instrumental_pitch_spinbox.setSuffix(" 半音")
        grid.addWidget(self.default_instrumental_pitch_spinbox, 2, 1)
        
        # 默认启用混响
        self.default_reverb_checkbox = QCheckBox("默认启用混响")
        grid.addWidget(self.default_reverb_checkbox, 3, 0, 1, 2)

        processing_layout.addLayout(grid)

        # 高级参数默认值
        advanced_group = QGroupBox("高级参数默认值")
        advanced_layout = QGridLayout(advanced_group)

        # 默认声码器
        advanced_layout.addWidget(QLabel("默认声码器:"), 0, 0)
        self.default_vocoder_combo = QComboBox()
        self.default_vocoder_combo.addItems(["pc_nsf_hifigan_testing", "nsf_hifigan", "pc_ddsp"])
        advanced_layout.addWidget(self.default_vocoder_combo, 0, 1)

        # 默认F0提取器
        advanced_layout.addWidget(QLabel("默认F0提取器:"), 1, 0)
        self.default_f0_extractor_combo = QComboBox()
        self.default_f0_extractor_combo.addItems(["rmvpe (默认)", "harvest", "crepe", "mangio-crepe"])
        advanced_layout.addWidget(self.default_f0_extractor_combo, 1, 1)

        # 默认共振峰偏移
        advanced_layout.addWidget(QLabel("默认共振峰偏移:"), 2, 0)
        self.default_formant_shift_spinbox = QSpinBox()
        self.default_formant_shift_spinbox.setRange(-30, 30)
        self.default_formant_shift_spinbox.setValue(0)
        advanced_layout.addWidget(self.default_formant_shift_spinbox, 2, 1)

        # 默认采样步数
        advanced_layout.addWidget(QLabel("默认采样步数:"), 3, 0)
        self.default_sampling_steps_spinbox = QSpinBox()
        self.default_sampling_steps_spinbox.setRange(1, 1000)
        self.default_sampling_steps_spinbox.setValue(50)
        advanced_layout.addWidget(self.default_sampling_steps_spinbox, 3, 1)

        # 默认采样器
        advanced_layout.addWidget(QLabel("默认采样器:"), 4, 0)
        self.default_sampler_combo = QComboBox()
        self.default_sampler_combo.addItems(["euler", "ddim", "dpmsolver", "unipc"])
        advanced_layout.addWidget(self.default_sampler_combo, 4, 1)

        # 默认设备
        advanced_layout.addWidget(QLabel("默认设备:"), 5, 0)
        self.default_device_combo = QComboBox()
        self.default_device_combo.addItems(["CPU", "CUDA", "Auto"])
        advanced_layout.addWidget(self.default_device_combo, 5, 1)

        processing_layout.addWidget(advanced_group)
        
        # 混响参数
        reverb_group = QGroupBox("默认混响参数")
        reverb_layout = QGridLayout(reverb_group)
        
        # 房间大小
        reverb_layout.addWidget(QLabel("房间大小:"), 0, 0)
        self.default_room_size_slider = QSlider(Qt.Horizontal)
        self.default_room_size_slider.setRange(0, 100)
        reverb_layout.addWidget(self.default_room_size_slider, 0, 1)
        self.room_size_value_label = QLabel("0.3")
        reverb_layout.addWidget(self.room_size_value_label, 0, 2)
        
        # 阻尼
        reverb_layout.addWidget(QLabel("阻尼:"), 1, 0)
        self.default_damping_slider = QSlider(Qt.Horizontal)
        self.default_damping_slider.setRange(0, 100)
        reverb_layout.addWidget(self.default_damping_slider, 1, 1)
        self.damping_value_label = QLabel("0.1")
        reverb_layout.addWidget(self.damping_value_label, 1, 2)
        
        # 湿声电平
        reverb_layout.addWidget(QLabel("湿声电平:"), 2, 0)
        self.default_wet_level_slider = QSlider(Qt.Horizontal)
        self.default_wet_level_slider.setRange(0, 100)
        reverb_layout.addWidget(self.default_wet_level_slider, 2, 1)
        self.wet_level_value_label = QLabel("0.2")
        reverb_layout.addWidget(self.wet_level_value_label, 2, 2)
        
        # 干声电平
        reverb_layout.addWidget(QLabel("干声电平:"), 3, 0)
        self.default_dry_level_slider = QSlider(Qt.Horizontal)
        self.default_dry_level_slider.setRange(0, 100)
        reverb_layout.addWidget(self.default_dry_level_slider, 3, 1)
        self.dry_level_value_label = QLabel("0.9")
        reverb_layout.addWidget(self.dry_level_value_label, 3, 2)
        
        # 连接滑块信号
        self.default_room_size_slider.valueChanged.connect(
            lambda v: self.room_size_value_label.setText(f"{v/100:.1f}")
        )
        self.default_damping_slider.valueChanged.connect(
            lambda v: self.damping_value_label.setText(f"{v/100:.1f}")
        )
        self.default_wet_level_slider.valueChanged.connect(
            lambda v: self.wet_level_value_label.setText(f"{v/100:.1f}")
        )
        self.default_dry_level_slider.valueChanged.connect(
            lambda v: self.dry_level_value_label.setText(f"{v/100:.1f}")
        )
        
        processing_layout.addWidget(reverb_group)
        parent_layout.addWidget(processing_frame)
    
    def _create_advanced_settings_section(self, parent_layout):
        """创建高级设置区域"""
        advanced_frame = self.create_section_frame("高级设置")
        advanced_layout = advanced_frame.layout()
        
        # 设置网格
        grid = QGridLayout()
        
        # 日志级别
        grid.addWidget(QLabel("日志级别:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        grid.addWidget(self.log_level_combo, 0, 1)
        
        # 自动清理临时文件
        self.auto_cleanup_checkbox = QCheckBox("自动清理临时文件")
        self.auto_cleanup_checkbox.setChecked(True)
        grid.addWidget(self.auto_cleanup_checkbox, 1, 0, 1, 2)
        
        # 启动时检查更新
        self.check_updates_checkbox = QCheckBox("启动时检查更新")
        self.check_updates_checkbox.setChecked(True)
        grid.addWidget(self.check_updates_checkbox, 2, 0, 1, 2)
        
        # 发送匿名使用统计
        self.send_analytics_checkbox = QCheckBox("发送匿名使用统计")
        grid.addWidget(self.send_analytics_checkbox, 3, 0, 1, 2)
        
        advanced_layout.addLayout(grid)
        parent_layout.addWidget(advanced_frame)
    
    def _create_action_buttons_section(self, parent_layout):
        """创建操作按钮区域"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 10, 0, 0)
        
        # 重置为默认值
        self.reset_button = QPushButton("🔄 重置为默认值")
        self.reset_button.clicked.connect(self._reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 应用设置
        self.apply_button = QPushButton("✅ 应用设置")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #56D364;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #46C454;
            }
        """)
        self.apply_button.clicked.connect(self._apply_settings)
        button_layout.addWidget(self.apply_button)
        
        # 保存设置
        self.save_button = QPushButton("💾 保存设置")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #58A6FF;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #79C0FF;
            }
        """)
        self.save_button.clicked.connect(self._save_settings)
        button_layout.addWidget(self.save_button)
        
        parent_layout.addWidget(button_frame)
    
    def _load_settings(self):
        """加载当前设置"""
        try:
            # 加载UI配置
            ui_config = self.config_manager.get_ui_config()
            self.window_width_spinbox.setValue(ui_config.window_width)
            self.window_height_spinbox.setValue(ui_config.window_height)
            self.remember_window_checkbox.setChecked(ui_config.remember_window_state)
            
            # 加载音频配置
            audio_config = self.config_manager.get_audio_config()
            self.sample_rate_combo.setCurrentText(f"{audio_config.sample_rate} Hz")
            self.output_format_combo.setCurrentText(audio_config.default_format.upper())
            self.max_file_size_spinbox.setValue(audio_config.max_file_size_mb)
            self.temp_dir_edit.setText(audio_config.temp_dir)
            
            # 加载处理配置
            processing_config = self.config_manager.get_processing_config()
            self.default_vocal_pitch_spinbox.setValue(processing_config.vocal_pitch)
            self.default_instrumental_pitch_spinbox.setValue(processing_config.instrumental_pitch)
            self.default_reverb_checkbox.setChecked(processing_config.reverb_enabled)
            
            # 设置混响参数
            self.default_room_size_slider.setValue(int(processing_config.reverb_room_size * 100))
            self.default_damping_slider.setValue(int(processing_config.reverb_damping * 100))
            self.default_wet_level_slider.setValue(int(processing_config.reverb_wet_level * 100))
            self.default_dry_level_slider.setValue(int(processing_config.reverb_dry_level * 100))

            # 设置高级参数
            self.default_vocoder_combo.setCurrentText(processing_config.vocoder)
            self.default_f0_extractor_combo.setCurrentText(processing_config.f0_extractor)
            self.default_formant_shift_spinbox.setValue(processing_config.formant_shift)
            self.default_sampling_steps_spinbox.setValue(processing_config.sampling_steps)
            self.default_sampler_combo.setCurrentText(processing_config.sampler)
            self.default_device_combo.setCurrentText(processing_config.device)

            self.logger.info("设置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def _update_model_list(self):
        """更新模型列表"""
        try:
            models_dir = Path("models")
            if models_dir.exists():
                model_files = list(models_dir.glob("*.pt"))
                model_names = [f.name for f in model_files]
                
                self.default_model_combo.clear()
                if model_names:
                    self.default_model_combo.addItems(model_names)
                else:
                    self.default_model_combo.addItem("未找到模型文件")
            else:
                self.default_model_combo.addItem("models目录不存在")
                
        except Exception as e:
            self.logger.error(f"更新模型列表失败: {e}")
    
    def _browse_temp_directory(self):
        """浏览临时文件目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择临时文件目录",
            self.temp_dir_edit.text()
        )
        
        if directory:
            self.temp_dir_edit.setText(directory)
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 这里可以实现实时应用设置的逻辑
            # 例如更新主题、语言等
            
            self.show_info_message("应用设置", "设置已应用（部分设置需要重启应用程序生效）")
            self.settings_changed.emit()
            
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
            self.show_error_message("应用失败", str(e))
    
    def _save_settings(self):
        """保存设置"""
        try:
            # 更新UI配置
            self.config_manager.ui_config.window_width = self.window_width_spinbox.value()
            self.config_manager.ui_config.window_height = self.window_height_spinbox.value()
            self.config_manager.ui_config.remember_window_state = self.remember_window_checkbox.isChecked()
            
            # 更新音频配置
            sample_rate_text = self.sample_rate_combo.currentText()
            sample_rate = int(sample_rate_text.split()[0])
            self.config_manager.audio_config.sample_rate = sample_rate
            self.config_manager.audio_config.default_format = self.output_format_combo.currentText().lower()
            self.config_manager.audio_config.max_file_size_mb = self.max_file_size_spinbox.value()
            self.config_manager.audio_config.temp_dir = self.temp_dir_edit.text()
            
            # 更新处理配置
            success = self.config_manager.update_processing_config(
                vocal_pitch=self.default_vocal_pitch_spinbox.value(),
                instrumental_pitch=self.default_instrumental_pitch_spinbox.value(),
                reverb_enabled=self.default_reverb_checkbox.isChecked(),
                reverb_room_size=self.default_room_size_slider.value() / 100.0,
                reverb_damping=self.default_damping_slider.value() / 100.0,
                reverb_wet_level=self.default_wet_level_slider.value() / 100.0,
                reverb_dry_level=self.default_dry_level_slider.value() / 100.0,
                # 高级参数
                vocoder=self.default_vocoder_combo.currentText(),
                f0_extractor=self.default_f0_extractor_combo.currentText(),
                formant_shift=self.default_formant_shift_spinbox.value(),
                sampling_steps=self.default_sampling_steps_spinbox.value(),
                sampler=self.default_sampler_combo.currentText(),
                device=self.default_device_combo.currentText()
            )
            
            if success:
                self.show_info_message("保存成功", "设置已保存")
                self.emit_status_message("设置已保存")
                self.settings_changed.emit()
            else:
                self.show_error_message("保存失败", "无法保存设置")
                
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            self.show_error_message("保存失败", str(e))
    
    def _reset_to_defaults(self):
        """重置为默认值"""
        from PySide6.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有设置为默认值吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 重置为默认配置
                self.config_manager.ui_config = self.config_manager.UIConfig()
                self.config_manager.audio_config = self.config_manager.AudioConfig()
                self.config_manager.processing_config = self.config_manager.ProcessingConfig()
                
                # 重新加载界面
                self._load_settings()
                
                self.show_info_message("重置完成", "所有设置已重置为默认值")
                self.settings_changed.emit()
                
            except Exception as e:
                self.logger.error(f"重置设置失败: {e}")
                self.show_error_message("重置失败", str(e))
