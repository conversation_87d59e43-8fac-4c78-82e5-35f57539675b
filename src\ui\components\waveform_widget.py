#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波形显示组件 - 提供音频波形的可视化显示和交互功能
"""

import logging
import numpy as np
from pathlib import Path
from typing import Optional

try:
    import pyqtgraph as pg
    from pydub import AudioSegment
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt, Signal, QPointF
from PySide6.QtGui import QFont, QColor


class WaveformWidget(QWidget):
    """波形显示组件"""
    
    # 信号定义
    position_clicked = Signal(float)  # 位置点击信号，参数为相对位置(0-1)
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化波形显示组件
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = logging.getLogger(__name__)
        
        # 检查依赖
        if not DEPENDENCIES_AVAILABLE:
            self._create_fallback_ui()
            return
        
        # 波形数据
        self.audio_samples = None
        self.sample_rate = None
        self.duration = 0
        self.current_position = 0  # 当前播放位置（0-1之间的比例）
        
        # 波形显示组件
        self.waveform_item = None
        self.position_line = None
        self.played_region = None
        
        # 创建界面
        self._create_ui()
        self._setup_theme()
        
        self.logger.debug("波形显示组件初始化完成")
    
    def _create_fallback_ui(self):
        """创建备用界面（当依赖不可用时）"""
        layout = QVBoxLayout(self)
        
        fallback_label = QLabel("波形显示不可用\n请安装 pyqtgraph 和 pydub")
        fallback_label.setAlignment(Qt.AlignCenter)
        fallback_label.setStyleSheet("""
            QLabel {
                color: #8B949E;
                background-color: #1C2128;
                border: 1px solid #30363D;
                border-radius: 8px;
                padding: 20px;
                font-size: 14px;
            }
        """)
        
        layout.addWidget(fallback_label)
        
        self.logger.warning("波形显示组件依赖不可用，使用备用界面")
    
    def _create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 配置pyqtgraph
        pg.setConfigOptions(antialias=True)
        
        # 创建绘图组件
        self.plot_widget = pg.PlotWidget(background='#1C2128')
        self.plot_widget.setFixedHeight(100)
        self.plot_widget.setStyleSheet("border: 1px solid #30363D; border-radius: 8px;")
        
        # 配置绘图属性
        self.plot_widget.plotItem.showGrid(False, False)
        self.plot_widget.plotItem.hideAxis('bottom')
        self.plot_widget.plotItem.hideAxis('left')
        self.plot_widget.plotItem.setMenuEnabled(False)
        
        # 禁用默认的交互功能
        self.plot_widget.setMouseEnabled(x=False, y=False)
        self.plot_widget.setMenuEnabled(False)
        
        # 连接鼠标点击事件
        self.plot_widget.scene().sigMouseClicked.connect(self._on_mouse_clicked)
        
        layout.addWidget(self.plot_widget)
        
        # 信息标签
        self.info_label = QLabel()
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("color: #8B949E; background-color: transparent;")
        self.info_label.setFont(QFont("Microsoft YaHei UI", 11))
        self.info_label.hide()
        
        layout.addWidget(self.info_label)
    
    def _setup_theme(self):
        """设置主题样式"""
        self.theme_colors = {
            'background': '#1C2128',
            'foreground': '#E6EDF3',
            'accent': '#58A6FF',
            'played': '#58A6FF',
            'unplayed': '#8B949E',
            'position_line': '#FFFFFF'
        }
    
    def load_audio(self, file_path: str) -> bool:
        """
        加载音频文件并生成波形
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 加载成功返回True
        """
        if not DEPENDENCIES_AVAILABLE:
            return False
        
        try:
            # 隐藏信息标签并清除当前波形
            self.info_label.hide()
            self.plot_widget.clear()
            
            # 检查文件是否存在
            if not Path(file_path).exists():
                self._show_error_message("文件不存在")
                return False
            
            # 加载音频文件
            audio = AudioSegment.from_file(file_path)
            self.sample_rate = audio.frame_rate
            self.duration = len(audio) / 1000.0  # 转换为秒
            
            # 获取音频采样并转为单声道
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2)).mean(axis=1)
            
            # 降采样以提高性能
            max_points = 2000
            if len(samples) > max_points:
                ratio = len(samples) // max_points
                samples = self._downsample_audio(samples, ratio)
            
            # 标准化波形振幅
            if len(samples) > 0 and np.max(np.abs(samples)) > 0:
                samples = samples / np.max(np.abs(samples)) * 0.8
            
            self.audio_samples = samples
            
            # 创建时间轴
            time_axis = np.linspace(0, self.duration, len(samples))
            
            # 绘制波形
            pen = pg.mkPen(color=self.theme_colors['unplayed'], width=1.2)
            self.waveform_item = self.plot_widget.plot(time_axis, samples, pen=pen)
            
            # 创建已播放区域
            brush_color = QColor(self.theme_colors['played'])
            brush_color.setAlpha(80)
            self.played_region = pg.LinearRegionItem(
                [0, 0],
                movable=False,
                brush=pg.mkBrush(color=brush_color),
                pen=pg.mkPen(color=self.theme_colors['played'], width=0)
            )
            self.plot_widget.addItem(self.played_region)
            
            # 添加当前位置指示线
            self.position_line = pg.InfiniteLine(
                pos=0,
                angle=90,
                pen=pg.mkPen(color=self.theme_colors['position_line'], width=1.2)
            )
            self.plot_widget.addItem(self.position_line)
            
            # 初始更新位置
            self.update_position(0)
            
            self.logger.info(f"波形加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"波形加载失败: {e}")
            self._show_error_message(f"加载失败: {str(e)[:30]}...")
            return False
    
    def _downsample_audio(self, samples: np.ndarray, ratio: int) -> np.ndarray:
        """
        音频降采样，使用峰值提取算法
        
        Args:
            samples: 音频采样数据
            ratio: 降采样比例
            
        Returns:
            np.ndarray: 降采样后的数据
        """
        n_chunks = len(samples) // ratio
        if n_chunks == 0:
            return samples
        
        result = np.zeros(n_chunks)
        
        for i in range(n_chunks):
            start = i * ratio
            end = min(start + ratio, len(samples))
            chunk = samples[start:end]
            
            # 提取块中的最大绝对值（峰值）
            max_val = np.max(np.abs(chunk))
            
            # 保留符号
            if np.max(chunk) >= abs(np.min(chunk)):
                result[i] = max_val
            else:
                result[i] = -max_val
        
        return result
    
    def update_position(self, relative_position: float):
        """
        更新当前播放位置
        
        Args:
            relative_position: 相对位置（0-1之间）
        """
        if not DEPENDENCIES_AVAILABLE:
            return
        
        self.current_position = relative_position
        
        # 更新位置线
        if self.position_line and self.duration > 0:
            self.position_line.setPos(relative_position * self.duration)
        
        # 更新已播放区域
        if self.played_region and self.duration > 0:
            self.played_region.setRegion([0, relative_position * self.duration])
    
    def _on_mouse_clicked(self, event):
        """处理鼠标点击事件"""
        if not DEPENDENCIES_AVAILABLE:
            return
        
        if self.plot_widget.sceneBoundingRect().contains(event.scenePos()):
            # 计算点击位置相对于波形的位置
            mouse_point = self.plot_widget.plotItem.vb.mapSceneToView(event.scenePos())
            x_pos = mouse_point.x()
            
            # 计算相对位置（0-1之间）
            if self.duration > 0:
                relative_pos = max(0, min(1, x_pos / self.duration))
                self.current_position = relative_pos
                self.update_position(relative_pos)
                self.position_clicked.emit(relative_pos)
    
    def _show_error_message(self, message: str):
        """显示错误消息"""
        if not DEPENDENCIES_AVAILABLE:
            return
        
        self.plot_widget.clear()
        self.info_label.setText(message)
        self.info_label.show()
    
    def clear(self):
        """清除波形显示"""
        if not DEPENDENCIES_AVAILABLE:
            return
        
        self.plot_widget.clear()
        self.audio_samples = None
        self.duration = 0
        self.current_position = 0
        self.waveform_item = None
        self.position_line = None
        self.played_region = None
