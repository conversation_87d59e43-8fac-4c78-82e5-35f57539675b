#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可折叠区域组件 - 实现类似HTML布局中的手风琴效果
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QFrame, QPropertyAnimation, QSizePolicy
)
from PySide6.QtCore import Qt, QEasingCurve, QPropertyAnimation, QRect, pyqtSignal
from PySide6.QtGui import QFont, QIcon


class CollapsibleSection(QWidget):
    """可折叠区域组件"""
    
    def __init__(self, title: str = "", icon_text: str = "", parent=None):
        """
        初始化可折叠区域
        
        Args:
            title: 区域标题
            icon_text: 图标文本（Material Icons字体）
            parent: 父组件
        """
        super().__init__(parent)
        
        self.title = title
        self.icon_text = icon_text
        self.is_expanded = False
        
        self._setup_ui()
        self._setup_style()
        
    def _setup_ui(self):
        """设置UI"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 标题按钮
        self.title_button = QPushButton()
        self.title_button.setCheckable(True)
        self.title_button.clicked.connect(self.toggle_expanded)
        
        # 标题布局
        title_layout = QHBoxLayout(self.title_button)
        title_layout.setContentsMargins(24, 16, 24, 16)
        
        # 图标和标题
        icon_title_layout = QHBoxLayout()
        icon_title_layout.setSpacing(12)
        
        if self.icon_text:
            self.icon_label = QLabel(self.icon_text)
            self.icon_label.setFont(QFont("Material Icons", 20))
            icon_title_layout.addWidget(self.icon_label)
        
        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        icon_title_layout.addWidget(self.title_label)
        
        title_layout.addLayout(icon_title_layout)
        title_layout.addStretch()
        
        # 展开/收起图标
        self.expand_icon = QLabel("expand_more")
        self.expand_icon.setFont(QFont("Material Icons", 20))
        title_layout.addWidget(self.expand_icon)
        
        self.main_layout.addWidget(self.title_button)
        
        # 内容容器
        self.content_container = QFrame()
        self.content_container.setMaximumHeight(0)
        self.content_container.setMinimumHeight(0)
        
        # 内容布局
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(8, 0, 8, 0)
        self.content_layout.setSpacing(0)
        
        self.main_layout.addWidget(self.content_container)
        
        # 动画
        self.animation = QPropertyAnimation(self.content_container, b"maximumHeight")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            CollapsibleSection {
                background-color: transparent;
            }
            
            QPushButton {
                background-color: #1F2937;
                color: #E5E7EB;
                font-weight: 600;
                border-radius: 12px;
                text-align: left;
                border: none;
            }
            
            QPushButton:hover {
                background-color: #374151;
            }
            
            QPushButton:pressed {
                background-color: #4B5563;
            }
            
            QLabel {
                color: #E5E7EB;
                background-color: transparent;
                border: none;
            }
            
            QFrame {
                background-color: transparent;
                border: none;
            }
        """)
        
    def add_content_widget(self, widget: QWidget):
        """添加内容组件"""
        self.content_layout.addWidget(widget)
        
    def toggle_expanded(self):
        """切换展开/收起状态"""
        self.is_expanded = not self.is_expanded
        
        if self.is_expanded:
            # 展开
            self.expand_icon.setText("expand_less")
            # 计算内容高度
            content_height = self.content_container.sizeHint().height()
            self.animation.setStartValue(0)
            self.animation.setEndValue(content_height)
        else:
            # 收起
            self.expand_icon.setText("expand_more")
            self.animation.setStartValue(self.content_container.maximumHeight())
            self.animation.setEndValue(0)
            
        self.animation.start()
        
    def set_expanded(self, expanded: bool):
        """设置展开状态"""
        if self.is_expanded != expanded:
            self.toggle_expanded()


class CollapsibleCard(QFrame):
    """可折叠卡片组件"""
    
    def __init__(self, parent=None):
        """初始化可折叠卡片"""
        super().__init__(parent)
        
        self._setup_ui()
        self._setup_style()
        
    def _setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(24, 24, 24, 24)
        self.layout.setSpacing(16)
        
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            CollapsibleCard {
                background-color: #1F2937;
                border-radius: 12px;
                border: none;
            }
        """)
        
    def add_widget(self, widget: QWidget):
        """添加组件"""
        self.layout.addWidget(widget)
