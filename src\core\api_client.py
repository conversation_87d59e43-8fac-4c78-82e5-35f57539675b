#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端 - 负责与本地/远程API服务的通信
"""

import logging
import requests
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import urljoin

from .config_manager import ConfigManager


class APIClient:
    """API客户端类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化API客户端
        
        Args:
            config_manager: 配置管理器实例
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 获取API配置
        self.api_config = config_manager.get_api_config()
        
        # 会话对象
        self.session = requests.Session()
        self.session.timeout = self.api_config.timeout
        
        self.logger.info("API客户端初始化完成")
    
    def _get_api_base_url(self) -> Optional[str]:
        """
        获取API基础URL
        
        Returns:
            str: API基础URL，无可用服务返回None
        """
        # 优先使用本地API
        if self.api_config.local_enabled:
            local_url = f"http://{self.api_config.local_host}:{self.api_config.local_port}"
            if self._test_connection(local_url):
                return local_url
        
        # 尝试远程API
        if self.api_config.remote_enabled and self.api_config.remote_url:
            if self._test_connection(self.api_config.remote_url):
                return self.api_config.remote_url
        
        # 如果启用了自动回退，再次尝试本地
        if self.api_config.auto_fallback and self.api_config.local_enabled:
            local_url = f"http://{self.api_config.local_host}:{self.api_config.local_port}"
            return local_url  # 返回本地URL，即使连接失败也尝试
        
        return None
    
    def _test_connection(self, base_url: str) -> bool:
        """
        测试API连接
        
        Args:
            base_url: API基础URL
            
        Returns:
            bool: 连接成功返回True
        """
        try:
            health_url = urljoin(base_url, "/health")
            response = self.session.get(health_url, timeout=5)
            return response.status_code == 200
            
        except Exception as e:
            self.logger.debug(f"连接测试失败 {base_url}: {e}")
            return False
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        发送API请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            dict: 响应数据，失败返回None
        """
        try:
            base_url = self._get_api_base_url()
            if not base_url:
                self.logger.error("无可用的API服务")
                return None
            
            url = urljoin(base_url, endpoint)
            
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败 {method} {endpoint}: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"API响应解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"API请求异常: {e}")
            return None
    
    def get_health_status(self) -> Optional[Dict[str, Any]]:
        """
        获取API服务健康状态
        
        Returns:
            dict: 健康状态信息
        """
        return self._make_request("GET", "/health")
    
    def get_available_models(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取可用模型列表
        
        Returns:
            list: 模型列表
        """
        result = self._make_request("GET", "/models/list")
        if result and result.get('success'):
            return result.get('models', [])
        return None
    
    def get_available_configs(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取可用配置文件列表
        
        Returns:
            list: 配置文件列表
        """
        result = self._make_request("GET", "/models/configs")
        if result and result.get('success'):
            return result.get('configs', [])
        return None
    
    def scan_models(self) -> Optional[Dict[str, Any]]:
        """
        重新扫描模型目录
        
        Returns:
            dict: 扫描结果
        """
        return self._make_request("GET", "/models/scan")
    
    def separate_audio(self, audio_file: str, mode: str = "vocals") -> Optional[Dict[str, Any]]:
        """
        音频分离
        
        Args:
            audio_file: 音频文件路径
            mode: 分离模式
            
        Returns:
            dict: 分离结果
        """
        try:
            if not Path(audio_file).exists():
                self.logger.error(f"音频文件不存在: {audio_file}")
                return None
            
            with open(audio_file, 'rb') as f:
                files = {'audio': f}
                data = {'mode': mode}
                
                result = self._make_request(
                    "POST", 
                    "/separate",
                    files=files,
                    data=data
                )
                
                return result
                
        except Exception as e:
            self.logger.error(f"音频分离请求失败: {e}")
            return None
    
    def convert_voice(
        self, 
        vocal_file: str, 
        model_file: str, 
        config_file: str, 
        params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        音色转换
        
        Args:
            vocal_file: 人声文件路径
            model_file: 模型文件名
            config_file: 配置文件名
            params: 转换参数
            
        Returns:
            dict: 转换结果
        """
        try:
            if not Path(vocal_file).exists():
                self.logger.error(f"人声文件不存在: {vocal_file}")
                return None
            
            with open(vocal_file, 'rb') as f:
                files = {'vocal': f}
                data = {
                    'model_file': model_file,
                    'config_file': config_file,
                    'params': json.dumps(params)
                }
                
                result = self._make_request(
                    "POST",
                    "/convert",
                    files=files,
                    data=data
                )
                
                return result
                
        except Exception as e:
            self.logger.error(f"音色转换请求失败: {e}")
            return None
    
    def mix_audio(
        self, 
        vocal_file: str, 
        instrumental_file: str, 
        params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        音频混音
        
        Args:
            vocal_file: 人声文件路径
            instrumental_file: 伴奏文件路径
            params: 混音参数
            
        Returns:
            dict: 混音结果
        """
        try:
            if not Path(vocal_file).exists():
                self.logger.error(f"人声文件不存在: {vocal_file}")
                return None
            
            if not Path(instrumental_file).exists():
                self.logger.error(f"伴奏文件不存在: {instrumental_file}")
                return None
            
            with open(vocal_file, 'rb') as vf, open(instrumental_file, 'rb') as inf:
                files = {
                    'vocal': vf,
                    'instrumental': inf
                }
                data = {
                    'params': json.dumps(params)
                }
                
                result = self._make_request(
                    "POST",
                    "/mix",
                    files=files,
                    data=data
                )
                
                return result
                
        except Exception as e:
            self.logger.error(f"音频混音请求失败: {e}")
            return None
    
    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 进度信息
        """
        return self._make_request("GET", f"/progress/{task_id}")
    
    def download_file(self, file_url: str, local_path: str) -> bool:
        """
        下载文件
        
        Args:
            file_url: 文件URL
            local_path: 本地保存路径
            
        Returns:
            bool: 下载成功返回True
        """
        try:
            response = self.session.get(file_url, stream=True)
            response.raise_for_status()
            
            local_path_obj = Path(local_path)
            local_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"文件下载成功: {file_url} -> {local_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件下载失败: {e}")
            return False
    
    def upload_file(self, file_path: str, endpoint: str) -> Optional[Dict[str, Any]]:
        """
        上传文件
        
        Args:
            file_path: 文件路径
            endpoint: 上传端点
            
        Returns:
            dict: 上传结果
        """
        try:
            if not Path(file_path).exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            with open(file_path, 'rb') as f:
                files = {'file': f}
                result = self._make_request("POST", endpoint, files=files)
                return result
                
        except Exception as e:
            self.logger.error(f"文件上传失败: {e}")
            return None
    
    def update_config(self):
        """更新API配置"""
        self.api_config = self.config_manager.get_api_config()
        self.session.timeout = self.api_config.timeout
        self.logger.info("API配置已更新")
