# 木偶AI翻唱 v2.0

基于PySide6的模块化AI翻唱应用程序，支持本地和云端音频处理。

## 🎯 项目概述

木偶AI翻唱 v2.0 是对原有PyQt5单文件应用的全面重构，采用现代化的模块化架构设计，将GUI客户端与音频处理服务分离，提供更好的性能、可维护性和用户体验。

### 主要特性

- 🎵 **选项卡式界面设计** - 歌曲管理、翻唱处理、API管理、设置四大功能模块
- 🔄 **云端/本地混合架构** - 支持本地API服务和远程云端处理，自动回退机制
- 🎤 **完整的翻唱流程** - 音频分离、AI音色转换、混响效果、音频混音
- 📊 **实时进度追踪** - WebSocket/HTTP轮询显示处理进度
- 🎧 **高级音频组件** - 优化的波形显示、多轨播放器、成品管理
- ⚙️ **模型管理系统** - 自动扫描models目录，动态加载模型和配置文件

## 🏗️ 项目结构

```
木偶AI翻唱/
├── src/                    # 源代码目录
│   ├── main.py            # 应用入口
│   ├── ui/                # 界面模块
│   │   ├── main_window.py # 主窗口
│   │   ├── tabs/          # 选项卡组件
│   │   │   ├── song_manager.py      # 歌曲管理
│   │   │   ├── audio_processor.py   # 翻唱处理
│   │   │   ├── api_manager.py       # API管理
│   │   │   └── settings.py          # 设置
│   │   └── components/    # 可复用组件
│   │       ├── audio_player.py      # 音频播放器
│   │       ├── waveform_widget.py   # 波形显示
│   │       └── progress_widget.py   # 进度显示
│   ├── core/              # 核心业务逻辑
│   │   ├── config_manager.py        # 配置管理
│   │   ├── audio_manager.py         # 音频管理
│   │   └── api_client.py            # API客户端
│   └── utils/             # 工具函数
│       ├── logging_utils.py         # 日志工具
│       ├── audio_utils.py           # 音频工具
│       └── file_utils.py            # 文件工具
├── api_server.py          # FastAPI服务器
├── config/                # 配置文件目录
├── models/                # AI模型目录
├── assets/                # 资源文件
├── temp/                  # 临时文件
├── outputs/               # 输出文件
└── logs/                  # 日志文件
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Windows 10+ (主要支持平台)
- 可选：NVIDIA GPU (CUDA支持，用于加速处理)

### 安装依赖

```bash
# 安装基础依赖
pip install PySide6 requests

# 安装音频处理依赖
pip install pydub pyqtgraph numpy

# 安装API服务依赖
pip install fastapi uvicorn

# 安装可选依赖
pip install librosa torch  # 高级音频处理
```

### 运行应用

1. **启动GUI客户端**
```bash
cd src
python main.py
```

2. **启动API服务器**（可选，支持本地处理）
```bash
python api_server.py --host 127.0.0.1 --port 8000
```

## 📖 使用指南

### 基本工作流程

1. **选择音频文件** - 在"翻唱处理"选项卡中上传或拖拽音频文件
2. **配置处理参数** - 选择AI模型、调节音高、设置混响等参数
3. **开始处理** - 点击"开始翻唱"按钮，实时查看处理进度
4. **预览结果** - 在"歌曲管理"选项卡中播放和管理成品歌曲

### 功能模块说明

#### 🎵 歌曲管理
- 成品歌曲的预览和播放
- 分轨音频独立播放控制（人声、伴奏、混音等）
- 歌曲元数据管理和组织
- 导入/导出功能

#### 🎤 翻唱处理
- 音频文件上传（支持拖拽）
- AI模型和配置文件选择
- 音高调节、混响设置
- 实时处理进度显示

#### 🔧 API管理
- 本地API服务启动/停止
- 远程API服务配置
- 连接测试和状态监控
- 服务日志查看

#### ⚙️ 设置
- 界面主题和语言设置
- 音频处理参数配置
- 默认模型和混响参数
- 高级选项和性能优化

## 🔧 配置说明

### API配置

应用支持本地和远程两种API服务模式：

- **本地模式**：在本机启动API服务，适合有GPU的用户
- **远程模式**：连接到云端API服务，适合CPU用户
- **自动回退**：本地失败时自动切换到远程服务

### 模型管理

将AI模型文件放置在`models/`目录下：
- 模型文件：`*.pt`
- 配置文件：`*.yaml`
- 应用会自动扫描并在界面中显示可用模型

## 🛠️ 开发说明

### 技术栈

- **GUI框架**：PySide6
- **API服务**：FastAPI + Uvicorn
- **音频处理**：pydub, librosa, numpy
- **波形显示**：pyqtgraph
- **配置管理**：JSON + dataclasses

### 架构设计

采用MVC架构模式：
- **Model**：核心业务逻辑和数据管理
- **View**：PySide6界面组件
- **Controller**：事件处理和业务协调

### 扩展开发

1. **添加新的选项卡**：继承`BaseTab`类
2. **添加新的组件**：放置在`ui/components/`目录
3. **添加新的API端点**：在`api_server.py`中定义
4. **添加新的工具函数**：放置在`utils/`目录

## 📝 更新日志

### v2.0.0 (当前版本)
- 🔄 完全重构为模块化PySide6应用
- 🆕 新增选项卡式界面设计
- 🆕 新增FastAPI后端服务
- 🆕 新增实时进度追踪
- 🆕 新增云端/本地混合架构
- 🆕 新增模型管理系统
- 🆕 新增成品歌曲管理功能
- ⚡ 性能优化和错误处理改进

### v1.x (原版本)
- PyQt5单文件应用
- 基础的音频处理功能
- 简单的界面设计

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢原版木偶AI翻唱的开发者
- 感谢PySide6和FastAPI社区
- 感谢所有贡献者和用户的支持

---

**注意**：本项目仅供个人娱乐和非商业用途使用。
